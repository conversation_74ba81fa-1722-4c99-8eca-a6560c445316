{"version": 3, "sources": ["file:///F:/SVN/plinko/BasePro/DemoMoveV3/assets/scripts/common/Config.ts"], "names": ["Config", "game", "JsonAsset", "re<PERSON><PERSON><PERSON><PERSON>", "oops", "GameConfig", "GameQueryConfig", "uiConfigData", "query", "gameNet", "configList", "init", "callback", "config_name", "load", "config", "get", "frameRate", "storage", "localDataKey", "localDataIv", "gui", "initConfigList"], "mappings": ";;;4JASaA,M;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATJC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AACNC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,e,iBAAAA,e;;AAGAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;AACT;wBACaP,M,GAAN,MAAMA,MAAN,CAAa;AAAA;AAEhB;AAFgB,eAGTC,IAHS;;AAKhB;AALgB,eAMTO,KANS;AAAA,eASTC,OATS;AAAA,eAWTC,UAXS,GAWc,EAXd;AAAA;;AAYTC,QAAAA,IAAI,CAACC,QAAD,EAAqB;AAC5B,cAAIC,WAAW,GAAG,eAAlB;AAEA;AAAA;AAAA,sCAAUC,IAAV,CAAeD,WAAf,EAA4BX,SAA5B,EAAuC,MAAM;AACzC,gBAAIa,MAAM,GAAG;AAAA;AAAA,wCAAUC,GAAV,CAAcH,WAAd,CAAb;AACA,iBAAKL,KAAL,GAAa;AAAA;AAAA,qDAAb;AACA,iBAAKP,IAAL,GAAY;AAAA;AAAA,0CAAec,MAAf,CAAZ,CAHyC,CAIzC;;AACAd,YAAAA,IAAI,CAACgB,SAAL,GAAiB,KAAKhB,IAAL,CAAUgB,SAA3B,CALyC,CAMzC;AACA;AACA;AACA;AAEA;;AACA;AAAA;AAAA,8BAAKC,OAAL,CAAaP,IAAb,CAAkB,KAAKV,IAAL,CAAUkB,YAA5B,EAA0C,KAAKlB,IAAL,CAAUmB,WAApD,EAZyC,CAazC;;AACA;AAAA;AAAA,8BAAKC,GAAL,CAASV,IAAT,CAAc;AAAA;AAAA,+CAAd;AACA,iBAAKW,cAAL;AACAV,YAAAA,QAAQ;AACX,WAjBD;AAkBH;;AACMU,QAAAA,cAAc,GAAG;AACpB,eAAKZ,UAAL,GAAkB,CACd,SADc,EAEd,QAFc,EAGd,KAHc,EAId,SAJc,EAKd,MALc,EAMd,YANc,EAOd,YAPc,EAQd,WARc,EASd,SATc,EAUd,gBAVc,EAWd,kBAXc,EAYd,cAZc,EAad,eAbc,CAAlB;AAeH;;AAlDe,O;;;;;;;;;;;;;;;;;;wBAqEPK,M,GAAS,IAAIf,MAAJ,E", "sourcesContent": ["import { game, JsonAsset } from \"cc\";\r\nimport { resLoader } from \"../loader/ResLoader\";\r\nimport { oops } from \"../core/Oop\";\r\nimport { GameConfig } from \"./GameConfig\";\r\nimport { GameQueryConfig } from \"./GameQueryConfig\";\r\nimport { GamePublicConfig } from \"./GamePublicConfig\";\r\nimport { GameNet } from \"../net/GameNet\";\r\nimport { uiConfigData } from \"./GameUIConfig\";\r\n/** 游戏配置静态访问类 */\r\nexport class Config {\r\n\r\n    /** 配置数据，版本号、支持语种等数据 */\r\n    public game!: GameConfig;\r\n\r\n    /** 处理浏览器地址栏参数，包括服务器ip、端口等数据 */\r\n    public query!: GameQueryConfig;\r\n\r\n\r\n    public gameNet!: GameNet;\r\n\r\n    public configList: string[] = [];\r\n    public init(callback: Function) {\r\n        let config_name = \"config/config\";\r\n\r\n        resLoader.load(config_name, JsonAsset, () => {\r\n            var config = resLoader.get(config_name);\r\n            this.query = new GameQueryConfig();\r\n            this.game = new GameConfig(config);\r\n            // 初始化每秒传输帧数\r\n            game.frameRate = this.game.frameRate;\r\n            // Http 服务器地址\r\n            // oops.http.server = this.game.httpServer;\r\n            //  Http 请求超时时间\r\n            // oops.http.timeout = this.game.httpTimeout;\r\n\r\n            // 初始化本地存储加密\r\n            oops.storage.init(this.game.localDataKey, this.game.localDataIv);\r\n            // 初始化界面窗口配置\r\n            oops.gui.init(uiConfigData());\r\n            this.initConfigList()\r\n            callback();\r\n        })\r\n    }\r\n    public initConfigList() {\r\n        this.configList = [\r\n            'itembuy',\r\n            'common',\r\n            'map',\r\n            'monster',\r\n            'item',\r\n            'lotterybuy',\r\n            'bossreward',\r\n            'cultivate',\r\n            'getdesc',\r\n            'cultivatelevel',\r\n            'magicboyactivity',\r\n            'exchangeitem',\r\n            'elixir_unseal',\r\n        ]\r\n    }\r\n}\r\n\r\nexport const enum xlsxName {\r\n    itembuy = \"ItemBuy\",\r\n    common = \"Common\",\r\n    map = \"Map\",\r\n    monster = \"Monster\",\r\n    item = \"Item\",\r\n    lotterybuy = \"BotteryBuy\",\r\n    bossreward = \"BossReward\",\r\n    cultivate = 'Cultivate',\r\n    getdesc = 'ItemGetDesc',\r\n    cultivatelevel = 'CultivateLevel',\r\n    magicboyactivity = 'MagicBoyActivity',\r\n    exchangeitem = \"ExchangeItem\",\r\n    elixir_unseal = \"ElixirUnseal\",\r\n}\r\n\r\nexport const config = new Config()"]}