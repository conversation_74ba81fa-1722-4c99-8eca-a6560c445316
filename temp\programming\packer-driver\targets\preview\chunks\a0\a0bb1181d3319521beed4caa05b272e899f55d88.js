System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, game, JsonAsset, resLoader, oops, GameConfig, GameQueryConfig, uiConfigData, Config, _crd, xlsxName, config;

  function _reportPossibleCrUseOfresLoader(extras) {
    _reporterNs.report("resLoader", "../loader/ResLoader", _context.meta, extras);
  }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../core/Oop", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "./GameConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameQueryConfig(extras) {
    _reporterNs.report("GameQueryConfig", "./GameQueryConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameNet(extras) {
    _reporterNs.report("GameNet", "../net/GameNet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfuiConfigData(extras) {
    _reporterNs.report("uiConfigData", "./GameUIConfig", _context.meta, extras);
  }

  _export("Config", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      game = _cc.game;
      JsonAsset = _cc.JsonAsset;
    }, function (_unresolved_2) {
      resLoader = _unresolved_2.resLoader;
    }, function (_unresolved_3) {
      oops = _unresolved_3.oops;
    }, function (_unresolved_4) {
      GameConfig = _unresolved_4.GameConfig;
    }, function (_unresolved_5) {
      GameQueryConfig = _unresolved_5.GameQueryConfig;
    }, function (_unresolved_6) {
      uiConfigData = _unresolved_6.uiConfigData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4d210VTqB9NjJGtuwo1ZIEd", "Config", undefined);

      __checkObsolete__(['game', 'JsonAsset']);

      /** 游戏配置静态访问类 */
      _export("Config", Config = class Config {
        constructor() {
          /** 配置数据，版本号、支持语种等数据 */
          this.game = void 0;

          /** 处理浏览器地址栏参数，包括服务器ip、端口等数据 */
          this.query = void 0;
          this.gameNet = void 0;
          this.configList = [];
        }

        init(callback) {
          var config_name = "config/config";
          (_crd && resLoader === void 0 ? (_reportPossibleCrUseOfresLoader({
            error: Error()
          }), resLoader) : resLoader).load(config_name, JsonAsset, () => {
            var config = (_crd && resLoader === void 0 ? (_reportPossibleCrUseOfresLoader({
              error: Error()
            }), resLoader) : resLoader).get(config_name);
            this.query = new (_crd && GameQueryConfig === void 0 ? (_reportPossibleCrUseOfGameQueryConfig({
              error: Error()
            }), GameQueryConfig) : GameQueryConfig)();
            this.game = new (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
              error: Error()
            }), GameConfig) : GameConfig)(config); // 初始化每秒传输帧数

            game.frameRate = this.game.frameRate; // Http 服务器地址
            // oops.http.server = this.game.httpServer;
            //  Http 请求超时时间
            // oops.http.timeout = this.game.httpTimeout;
            // 初始化本地存储加密

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).storage.init(this.game.localDataKey, this.game.localDataIv); // 初始化界面窗口配置

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.init((_crd && uiConfigData === void 0 ? (_reportPossibleCrUseOfuiConfigData({
              error: Error()
            }), uiConfigData) : uiConfigData)());
            this.initConfigList();
            callback();
          });
        }

        initConfigList() {
          this.configList = ['itembuy', 'common', 'map', 'monster', 'item', 'lotterybuy', 'bossreward', 'cultivate', 'getdesc', 'cultivatelevel', 'magicboyactivity', 'exchangeitem', 'elixir_unseal'];
        }

      });

      _export("xlsxName", xlsxName = {
        itembuy: "ItemBuy",
        common: "Common",
        map: "Map",
        monster: "Monster",
        item: "Item",
        lotterybuy: "BotteryBuy",
        bossreward: "BossReward",
        cultivate: "Cultivate",
        getdesc: "ItemGetDesc",
        cultivatelevel: "CultivateLevel",
        magicboyactivity: "MagicBoyActivity",
        exchangeitem: "ExchangeItem",
        elixir_unseal: "ElixirUnseal"
      });

      _export("config", config = new Config());

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=a0bb1181d3319521beed4caa05b272e899f55d88.js.map