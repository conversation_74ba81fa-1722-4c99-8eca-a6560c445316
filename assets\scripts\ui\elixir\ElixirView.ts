import { _decorator, Component, instantiate, Label, Node, Prefab, sp, Sprite, SpriteFrame, tween, UITransform, Vec3, v3 } from 'cc';
import { lobby, yy } from 'yycore';
import { oops } from '../../core/Oop';
import { ELIXIR_MAP_LIST, ITEM } from '../../common/GameUtils';
import { itemcfg } from '../../game/model/ItemCfg';
import { ElixirViewItem } from './ElixirViewItem';
import { DigTreasure } from '../../proto/DigTreasureProtocol_client';
import { Message } from '../../event/MessageManager';
import { resLoader } from '../../loader/ResLoader';
import { config } from '../../common/Config';
import { ViewUtil } from '../../util/ViewUtil';
import { elixirUnsealCfg } from '../../game/model/ElixirUnsealCfg';
const { ccclass, property } = _decorator;

@ccclass('ElixirView')
export class ElixirView extends Component {

    @property(Prefab)
    private pbItem: Prefab = null;
    @property(Node)
    private ndElixirArea: Node = null;
    @property(Label)
    private labTimer: Label = null;
    @property(Label)
    private labLingqi: Label = null;

    @property(Node)
    private ndBeforeKaiDing: Node = null;
    @property(Node)
    private ndAfterKaiDing: Node = null;
    @property(Node)
    private ndZiYangArea: Node = null;
    @property(Node)
    private ndResultArea: Node = null;

    @property(Node)
    private ndOpenArea: Node = null;
    @property(sp.Skeleton)
    private skOpenBlankArea: sp.Skeleton = null;
    @property(Node)
    private ndLines: Node = null;
    @property(Node)
    private ndItems: Node = null;

    @property(sp.Skeleton)
    private skeLuzi: sp.Skeleton = null;

    private _kaidingData: DigTreasure.PtNextKaiDingSubInfo = null;

    private _luziNoot: Node = null;

    private _resultPrefab: Node = null;
    private _linePrefab: Node = null;
    private _itemPrefab: Node = null;
    private _giftPrefab: Node = null;

    private _moveNum: number = 0;

    onLoad() {
        Message.on("on_elixir_auto_kaiding_begin", this.onKaidingBegin, this);
        Message.on("on_elixir_auto_kaiding_finish", this.onKaidingFinish, this);
        Message.on("on_elixir_once_kaiding_begin", this.onKaidingBegin, this);
        Message.on("on_elixir_once_kaiding_finish", this.onKaidingFinish, this);

        yy.event.on(this, lobby.events.MyPropsChanged, this.onUpdateProps);

        this._luziNoot = this.skeLuzi.node.parent;

        this._resultPrefab = instantiate(this.ndResultArea.children[0]);
        this.ndResultArea.destroyAllChildren();

        this._linePrefab = instantiate(this.ndLines.children[0]);
        this.ndLines.destroyAllChildren();
        const ndItem = this.ndItems.children[0];
        const ndGift = ndItem.children[0];
        this._giftPrefab = instantiate(ndGift);
        ndItem.destroyAllChildren();
        this._itemPrefab = instantiate(ndItem);
        this.ndItems.destroyAllChildren();
    }

    onDestroy() {
        Message.off("on_elixir_auto_kaiding_begin", this.onKaidingBegin, this);
        Message.off("on_elixir_auto_kaiding_finish", this.onKaidingFinish, this);
        Message.off("on_elixir_once_kaiding_begin", this.onKaidingBegin, this);
        Message.off("on_elixir_once_kaiding_finish", this.onKaidingFinish, this);

        yy.event.off(lobby.events.MyPropsChanged, this.onUpdateProps);
    }

    onAdded(data) {
        const kaidingData = oops.game.getNextAutoKaiDingInfo(oops.game.getEMapType());
        this._kaidingData = kaidingData;

        this.ndElixirArea.destroyAllChildren();

        const lMapType = oops.game.getLMapType();
        const itemIdList = ELIXIR_MAP_LIST[lMapType];
        itemIdList.forEach(itemId => {
            const itemDataCfg = itemcfg.getDataByIndex(itemId);
            const itemNode = instantiate(this.pbItem);
            this.ndElixirArea.addChild(itemNode);

            const itemComp = itemNode.getComponent(ElixirViewItem);
            itemComp.init(itemDataCfg);
        });

        this.updateTimer();
        this.updateLingqi();

        this.ndBeforeKaiDing.active = true;
        this.ndAfterKaiDing.active = false;
        this.ndZiYangArea.active = true;
        this.ndResultArea.active = false;
        this.ndOpenArea.active = false;
    }

    start() {

    }

    update(dt: number) {
        this.updateTimer();
    }

    protected onCloseClick() {
        oops.gui.removeByNode(this.node)
    }

    protected onBtnManulKaiDing() {
        oops.game.m_gameNet.Send_ReqOnceKaiDing();
    }

    protected onBtnUseAll() {
        console.debug('onBtnUseAll');
        // 测试移动小球功能
        this.moveElixirBallsToSlots();
    }

    private updateTimer() {
        if (this._kaidingData) {
            const curTime = Date.now() + oops.game.getServerTimeOffset();
            const timeData = Math.floor((this._kaidingData.nextKaiDingTime.toNumber() * 1000 - curTime) / 1000);
            const minutes: number = Math.floor(timeData / 60);
            const remainingSeconds: number = timeData % 60;
            const formattedMinutes: string = minutes.toString().length < 2 ? `0${minutes.toString()}` : minutes.toString();
            const formattedSeconds: string = remainingSeconds.toString().length < 2 ? `0${remainingSeconds.toString()}` : remainingSeconds.toString();
            this.labTimer.string = "开鼎时间 " + (minutes <= 0 && remainingSeconds <= 0 ? "00:00" : `${formattedMinutes}:${formattedSeconds}`);
        }
    }

    private updateLingqi() {
        const eMapType = oops.game.getEMapType();
        let count = 0;
        switch (eMapType) {
            case DigTreasure.eMapType.eMT_Shan2:
                count = lobby.myBag.getPropCountByID(ITEM.LINGQI_1) | 0;
                break;
            case DigTreasure.eMapType.eMT_Dong2:
                count = lobby.myBag.getPropCountByID(ITEM.LINGQI_2) | 0;
                break;
            case DigTreasure.eMapType.eMT_Ling2:
                count = lobby.myBag.getPropCountByID(ITEM.LINGQI_3) | 0;
                break;
        }
        this.labLingqi.string = count.toString();
    }

    private onKaidingBegin(event, data: { batId: string, reclaimXianDanList: DigTreasure.PtGiftAList, bool?: boolean }) {
        // this.ndBeforeKaiDing.active = false;
        // this.ndAfterKaiDing.active = true;
        // this.ndZiYangArea.active = false;
        // this.ndResultArea.active = true;
        // this.ndResultArea.destroyAllChildren();

        this.skeLuzi.setAnimation(0, "kai", false);
        this.scheduleOnce(() => {
            this.ndOpenArea.active = true;
            this.moveElixirBallsToSlots();
        }, 1.2);
    }

    private onKaidingFinish(event, data: { kaidingPlan: DigTreasure.PtAutoKaiDingPlan, kaidingMingxi: DigTreasure.PtPlayerKaiDingMingXiA }) {
        const elixirList = ELIXIR_MAP_LIST[oops.game.getLMapType()];

        let o
        data.kaidingPlan.kaiDingResults.forEach((result, index) => {
            const elixirId = elixirList[result];
            const sp = this.ndOpenArea.children[index].getComponent(Sprite);
            const itemDataCfg = itemcfg.getDataByIndex(elixirId);
            this.loadSp(`icon/elixir/${itemDataCfg.res}/spriteFrame`, sp);
        });

        this.ndResultArea.destroyAllChildren();
        data.kaidingPlan.zhanWeiGiftAList.forEach((gift, index) => {
            const elixirId = elixirList[index];
            const costCount = data.kaidingPlan.xianDanCostCounts[index] || 0;
            this.loadResultItem(elixirId, costCount, gift);
        });
    }

    private loadSp(iconPath: string, spriteNode: Sprite) {
        resLoader.load(config.game.bundleName, iconPath, SpriteFrame, (err, spFrame: SpriteFrame) => {
            if (err) {
                return
            }
            spriteNode.spriteFrame = spFrame;
        });
    }

    private loadResultItem(elixirId: number, count: number, gift: DigTreasure.PtZhanWeiGiftA) {
        const ndItem = instantiate(this._resultPrefab);
        this.ndResultArea.addChild(ndItem);

        const elixirIcon = ndItem.getChildByName("elixir_icon").getComponent(Sprite);
        const elixirCount = ndItem.getChildByName("elixir_count").getComponent(Label);
        const elixirDataCfg = itemcfg.getDataByIndex(elixirId);
        this.loadSp(`icon/elixir/${elixirDataCfg.res}/spriteFrame`, elixirIcon);
        elixirCount.string = `${count}`;

        const giftIcon = ndItem.getChildByName("gift_icon").getComponent(Sprite);
        const giftCount = ndItem.getChildByName("gift_count").getComponent(Label);
        const giftDataCfg = itemcfg.getDataByIndex(parseInt(gift.giftId));
        this.loadSp(`icon/item/${giftDataCfg.res}/spriteFrame`, giftIcon);
        giftCount.string = `${gift.giftNum}`;
    }

    private onUpdateProps() {
        this.updateLingqi();
    }

    /**
     * 移动红框中的4个小球到黄框中对应的坑位
     * 从 OpenNode 下的 icon_elixir_01-04 移动到 OpenBox 下的 icon_elixir_bg_1-4
     * @param duration 动画持续时间，默认0.8秒
     * @param delayBetween 每个小球之间的延迟时间，默认0.1秒
     */
    public moveElixirBallsToSlots(duration: number = 0.8, delayBetween: number = 0.1) {
        console.log("找到 OpenNode 和 OpenBox 节点，开始移动小球");

        // 定义小球和坑位的对应关系
        const ballSlotPairs = [
            { ballName: "icon_elixir_01", slotName: "icon_elixir_bg_1" },
            { ballName: "icon_elixir_02", slotName: "icon_elixir_bg_2" },
            { ballName: "icon_elixir_03", slotName: "icon_elixir_bg_3" },
            { ballName: "icon_elixir_04", slotName: "icon_elixir_bg_4" }
        ];

        // 遍历每个小球和对应的坑位
        ballSlotPairs.forEach((pair, index) => {
            const ballNode = this.ndOpenArea.getChildByName(pair.ballName);
            const slotNode = this.skOpenBlankArea.node.getChildByName(pair.slotName);
            this.moveBallToSlot(ballNode, slotNode, index * delayBetween, duration);
        });
    }

    /**
     * 移动单个小球到指定坑位
     * @param ballNode 小球节点
     * @param slotNode 坑位节点
     * @param delay 延迟时间
     * @param duration 动画持续时间
     */
    private moveBallToSlot(ballNode: Node, slotNode: Node, delay: number = 0, duration: number = 0.8) {
        // 获取坑位在世界坐标系中的位置
        const slotWorldPos = slotNode.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO);
        // 将坑位的世界坐标转换为小球父节点的本地坐标
        const targetLocalPos = ballNode.parent!.getComponent(UITransform)!.convertToNodeSpaceAR(slotWorldPos);

        console.log(`准备移动小球 ${ballNode.name} 从 (${ballNode.position.x}, ${ballNode.position.y}) 到 (${targetLocalPos.x}, ${targetLocalPos.y})`);

        // 使用 tween 动画移动小球
        tween(ballNode)
            .delay(delay)
            .to(duration, { position: targetLocalPos }, { easing: 'cubicOut' })
            .call(() => {
                console.log(`小球 ${ballNode.name} 已移动到坑位 ${slotNode.name}`);
                this._moveNum++;
                this.switchToAfterKaiDing(this._moveNum);
            })
            .start();
    }

    private switchToAfterKaiDing(count: number) {
        if (count == 1) {
            this.ndBeforeKaiDing.active = false;
            this.ndAfterKaiDing.active = true;
            this.ndZiYangArea.active = false;
            this.ndResultArea.active = true;
            this._luziNoot.active = false;
        }

        if (count == 4) {
            this.skOpenBlankArea.setAnimation(0, "kai", false);

            // for (let i = 0; i < 4; i++) {
            //     const lineNode = instantiate(this._linePrefab);
            //     this.ndLines.addChild(lineNode);
            //     const itemNode = instantiate(this._itemPrefab);
            //     this.ndItems.addChild(itemNode);

            //     const dataList = elixirUnsealCfg.getData(oops.game.getEMapType(), i);
            //     for (let j = 0; j < dataList.length; j++) {
            //         const giftNode = instantiate(this._giftPrefab);
            //         itemNode.addChild(giftNode);
            //     }
            // }
        }
    }

}


