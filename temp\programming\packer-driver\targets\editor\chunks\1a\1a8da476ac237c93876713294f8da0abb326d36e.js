System.register(["__unresolved_0", "cc", "yycore", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, Label, Node, Prefab, sp, Sprite, SpriteFrame, tween, UITransform, Vec3, lobby, yy, oops, ELIXIR_MAP_LIST, ITEM, itemcfg, ElixirViewItem, DigTreasure, Message, resLoader, config, elixirUnsealCfg, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _crd, ccclass, property, ElixirView;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOflobby(extras) {
    _reporterNs.report("lobby", "yycore", _context.meta, extras);
  }

  function _reportPossibleCrUseOfyy(extras) {
    _reporterNs.report("yy", "yycore", _context.meta, extras);
  }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../core/Oop", _context.meta, extras);
  }

  function _reportPossibleCrUseOfELIXIR_MAP_LIST(extras) {
    _reporterNs.report("ELIXIR_MAP_LIST", "../../common/GameUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfITEM(extras) {
    _reporterNs.report("ITEM", "../../common/GameUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfitemcfg(extras) {
    _reporterNs.report("itemcfg", "../../game/model/ItemCfg", _context.meta, extras);
  }

  function _reportPossibleCrUseOfElixirViewItem(extras) {
    _reporterNs.report("ElixirViewItem", "./ElixirViewItem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDigTreasure(extras) {
    _reporterNs.report("DigTreasure", "../../proto/DigTreasureProtocol_client", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMessage(extras) {
    _reporterNs.report("Message", "../../event/MessageManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfresLoader(extras) {
    _reporterNs.report("resLoader", "../../loader/ResLoader", _context.meta, extras);
  }

  function _reportPossibleCrUseOfconfig(extras) {
    _reporterNs.report("config", "../../common/Config", _context.meta, extras);
  }

  function _reportPossibleCrUseOfelixirUnsealCfg(extras) {
    _reporterNs.report("elixirUnsealCfg", "../../game/model/ElixirUnsealCfg", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Label = _cc.Label;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      sp = _cc.sp;
      Sprite = _cc.Sprite;
      SpriteFrame = _cc.SpriteFrame;
      tween = _cc.tween;
      UITransform = _cc.UITransform;
      Vec3 = _cc.Vec3;
    }, function (_yycore) {
      lobby = _yycore.lobby;
      yy = _yycore.yy;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      ELIXIR_MAP_LIST = _unresolved_3.ELIXIR_MAP_LIST;
      ITEM = _unresolved_3.ITEM;
    }, function (_unresolved_4) {
      itemcfg = _unresolved_4.itemcfg;
    }, function (_unresolved_5) {
      ElixirViewItem = _unresolved_5.ElixirViewItem;
    }, function (_unresolved_6) {
      DigTreasure = _unresolved_6.DigTreasure;
    }, function (_unresolved_7) {
      Message = _unresolved_7.Message;
    }, function (_unresolved_8) {
      resLoader = _unresolved_8.resLoader;
    }, function (_unresolved_9) {
      config = _unresolved_9.config;
    }, function (_unresolved_10) {
      elixirUnsealCfg = _unresolved_10.elixirUnsealCfg;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6ca8b6QTllC2p7cQBeU57N/", "ElixirView", undefined);

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'Label', 'Node', 'Prefab', 'sp', 'Sprite', 'SpriteFrame', 'tween', 'UITransform', 'Vec3', 'v3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("ElixirView", ElixirView = (_dec = ccclass('ElixirView'), _dec2 = property(Prefab), _dec3 = property(Node), _dec4 = property(Label), _dec5 = property(Label), _dec6 = property(Node), _dec7 = property(Node), _dec8 = property(Node), _dec9 = property(Node), _dec10 = property(Node), _dec11 = property(sp.Skeleton), _dec12 = property(Node), _dec13 = property(Node), _dec14 = property(sp.Skeleton), _dec(_class = (_class2 = class ElixirView extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "pbItem", _descriptor, this);

          _initializerDefineProperty(this, "ndElixirArea", _descriptor2, this);

          _initializerDefineProperty(this, "labTimer", _descriptor3, this);

          _initializerDefineProperty(this, "labLingqi", _descriptor4, this);

          _initializerDefineProperty(this, "ndBeforeKaiDing", _descriptor5, this);

          _initializerDefineProperty(this, "ndAfterKaiDing", _descriptor6, this);

          _initializerDefineProperty(this, "ndZiYangArea", _descriptor7, this);

          _initializerDefineProperty(this, "ndResultArea", _descriptor8, this);

          _initializerDefineProperty(this, "ndOpenArea", _descriptor9, this);

          _initializerDefineProperty(this, "skOpenBlankArea", _descriptor10, this);

          _initializerDefineProperty(this, "ndLines", _descriptor11, this);

          _initializerDefineProperty(this, "ndItems", _descriptor12, this);

          _initializerDefineProperty(this, "skeLuzi", _descriptor13, this);

          this._kaidingData = null;
          this._luziNoot = null;
          this._resultPrefab = null;
          this._linePrefab = null;
          this._itemPrefab = null;
          this._giftPrefab = null;
          this._moveNum = 0;
        }

        onLoad() {
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).on("on_elixir_auto_kaiding_begin", this.onKaidingBegin, this);
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).on("on_elixir_auto_kaiding_finish", this.onKaidingFinish, this);
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).on("on_elixir_once_kaiding_begin", this.onKaidingBegin, this);
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).on("on_elixir_once_kaiding_finish", this.onKaidingFinish, this);
          (_crd && yy === void 0 ? (_reportPossibleCrUseOfyy({
            error: Error()
          }), yy) : yy).event.on(this, (_crd && lobby === void 0 ? (_reportPossibleCrUseOflobby({
            error: Error()
          }), lobby) : lobby).events.MyPropsChanged, this.onUpdateProps);
          this._luziNoot = this.skeLuzi.node.parent;
          this._resultPrefab = instantiate(this.ndResultArea.children[0]);
          this.ndResultArea.destroyAllChildren();
          this._linePrefab = instantiate(this.ndLines.children[0]);
          this.ndLines.destroyAllChildren();
          const ndItem = this.ndItems.children[0];
          const ndGift = ndItem.children[0];
          this._giftPrefab = instantiate(ndGift);
          ndItem.destroyAllChildren();
          this._itemPrefab = instantiate(ndItem);
          this.ndItems.destroyAllChildren();
        }

        onDestroy() {
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).off("on_elixir_auto_kaiding_begin", this.onKaidingBegin, this);
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).off("on_elixir_auto_kaiding_finish", this.onKaidingFinish, this);
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).off("on_elixir_once_kaiding_begin", this.onKaidingBegin, this);
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).off("on_elixir_once_kaiding_finish", this.onKaidingFinish, this);
          (_crd && yy === void 0 ? (_reportPossibleCrUseOfyy({
            error: Error()
          }), yy) : yy).event.off((_crd && lobby === void 0 ? (_reportPossibleCrUseOflobby({
            error: Error()
          }), lobby) : lobby).events.MyPropsChanged, this.onUpdateProps);
        }

        onAdded(data) {
          const kaidingData = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getNextAutoKaiDingInfo((_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getEMapType());
          this._kaidingData = kaidingData;
          this.ndElixirArea.destroyAllChildren();
          const lMapType = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getLMapType();
          const itemIdList = (_crd && ELIXIR_MAP_LIST === void 0 ? (_reportPossibleCrUseOfELIXIR_MAP_LIST({
            error: Error()
          }), ELIXIR_MAP_LIST) : ELIXIR_MAP_LIST)[lMapType];
          itemIdList.forEach(itemId => {
            const itemDataCfg = (_crd && itemcfg === void 0 ? (_reportPossibleCrUseOfitemcfg({
              error: Error()
            }), itemcfg) : itemcfg).getDataByIndex(itemId);
            const itemNode = instantiate(this.pbItem);
            this.ndElixirArea.addChild(itemNode);
            const itemComp = itemNode.getComponent(_crd && ElixirViewItem === void 0 ? (_reportPossibleCrUseOfElixirViewItem({
              error: Error()
            }), ElixirViewItem) : ElixirViewItem);
            itemComp.init(itemDataCfg);
          });
          this.updateTimer();
          this.updateLingqi();
          this.ndBeforeKaiDing.active = true;
          this.ndAfterKaiDing.active = false;
          this.ndZiYangArea.active = true;
          this.ndResultArea.active = false;
          this.ndOpenArea.active = false;
        }

        start() {}

        update(dt) {
          this.updateTimer();
        }

        onCloseClick() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.removeByNode(this.node);
        }

        onBtnManulKaiDing() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.m_gameNet.Send_ReqOnceKaiDing();
        }

        onBtnUseAll() {
          console.debug('onBtnUseAll'); // 测试移动小球功能

          this.moveElixirBallsToSlots();
        }

        updateTimer() {
          if (this._kaidingData) {
            const curTime = Date.now() + (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).game.getServerTimeOffset();
            const timeData = Math.floor((this._kaidingData.nextKaiDingTime.toNumber() * 1000 - curTime) / 1000);
            const minutes = Math.floor(timeData / 60);
            const remainingSeconds = timeData % 60;
            const formattedMinutes = minutes.toString().length < 2 ? `0${minutes.toString()}` : minutes.toString();
            const formattedSeconds = remainingSeconds.toString().length < 2 ? `0${remainingSeconds.toString()}` : remainingSeconds.toString();
            this.labTimer.string = "开鼎时间 " + (minutes <= 0 && remainingSeconds <= 0 ? "00:00" : `${formattedMinutes}:${formattedSeconds}`);
          }
        }

        updateLingqi() {
          const eMapType = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getEMapType();
          let count = 0;

          switch (eMapType) {
            case (_crd && DigTreasure === void 0 ? (_reportPossibleCrUseOfDigTreasure({
              error: Error()
            }), DigTreasure) : DigTreasure).eMapType.eMT_Shan2:
              count = (_crd && lobby === void 0 ? (_reportPossibleCrUseOflobby({
                error: Error()
              }), lobby) : lobby).myBag.getPropCountByID((_crd && ITEM === void 0 ? (_reportPossibleCrUseOfITEM({
                error: Error()
              }), ITEM) : ITEM).LINGQI_1) | 0;
              break;

            case (_crd && DigTreasure === void 0 ? (_reportPossibleCrUseOfDigTreasure({
              error: Error()
            }), DigTreasure) : DigTreasure).eMapType.eMT_Dong2:
              count = (_crd && lobby === void 0 ? (_reportPossibleCrUseOflobby({
                error: Error()
              }), lobby) : lobby).myBag.getPropCountByID((_crd && ITEM === void 0 ? (_reportPossibleCrUseOfITEM({
                error: Error()
              }), ITEM) : ITEM).LINGQI_2) | 0;
              break;

            case (_crd && DigTreasure === void 0 ? (_reportPossibleCrUseOfDigTreasure({
              error: Error()
            }), DigTreasure) : DigTreasure).eMapType.eMT_Ling2:
              count = (_crd && lobby === void 0 ? (_reportPossibleCrUseOflobby({
                error: Error()
              }), lobby) : lobby).myBag.getPropCountByID((_crd && ITEM === void 0 ? (_reportPossibleCrUseOfITEM({
                error: Error()
              }), ITEM) : ITEM).LINGQI_3) | 0;
              break;
          }

          this.labLingqi.string = count.toString();
        }

        onKaidingBegin(event, data) {
          // this.ndBeforeKaiDing.active = false;
          // this.ndAfterKaiDing.active = true;
          // this.ndZiYangArea.active = false;
          // this.ndResultArea.active = true;
          // this.ndResultArea.destroyAllChildren();
          this.skeLuzi.setAnimation(0, "kai", false);
          this.scheduleOnce(() => {
            this.ndOpenArea.active = true;
            this.moveElixirBallsToSlots();
          }, 1.2);
        }

        onKaidingFinish(event, data) {
          const elixirList = (_crd && ELIXIR_MAP_LIST === void 0 ? (_reportPossibleCrUseOfELIXIR_MAP_LIST({
            error: Error()
          }), ELIXIR_MAP_LIST) : ELIXIR_MAP_LIST)[(_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getLMapType()];
          data.kaidingPlan.kaiDingResults.forEach((result, index) => {
            const elixirId = elixirList[result];
            const sp = this.ndOpenArea.children[index].getComponent(Sprite);
            const itemDataCfg = (_crd && itemcfg === void 0 ? (_reportPossibleCrUseOfitemcfg({
              error: Error()
            }), itemcfg) : itemcfg).getDataByIndex(elixirId);
            this.loadSp(`icon/elixir/${itemDataCfg.res}/spriteFrame`, sp);
          });
          this.ndResultArea.destroyAllChildren();
          data.kaidingPlan.zhanWeiGiftAList.forEach((gift, index) => {
            const elixirId = elixirList[index];
            const costCount = data.kaidingPlan.xianDanCostCounts[index] || 0;
            this.loadResultItem(elixirId, costCount, gift);
          });
        }

        loadSp(iconPath, spriteNode) {
          (_crd && resLoader === void 0 ? (_reportPossibleCrUseOfresLoader({
            error: Error()
          }), resLoader) : resLoader).load((_crd && config === void 0 ? (_reportPossibleCrUseOfconfig({
            error: Error()
          }), config) : config).game.bundleName, iconPath, SpriteFrame, (err, spFrame) => {
            if (err) {
              return;
            }

            spriteNode.spriteFrame = spFrame;
          });
        }

        loadResultItem(elixirId, count, gift) {
          const ndItem = instantiate(this._resultPrefab);
          this.ndResultArea.addChild(ndItem);
          const elixirIcon = ndItem.getChildByName("elixir_icon").getComponent(Sprite);
          const elixirCount = ndItem.getChildByName("elixir_count").getComponent(Label);
          const elixirDataCfg = (_crd && itemcfg === void 0 ? (_reportPossibleCrUseOfitemcfg({
            error: Error()
          }), itemcfg) : itemcfg).getDataByIndex(elixirId);
          this.loadSp(`icon/elixir/${elixirDataCfg.res}/spriteFrame`, elixirIcon);
          elixirCount.string = `${count}`;
          const giftIcon = ndItem.getChildByName("gift_icon").getComponent(Sprite);
          const giftCount = ndItem.getChildByName("gift_count").getComponent(Label);
          const giftDataCfg = (_crd && itemcfg === void 0 ? (_reportPossibleCrUseOfitemcfg({
            error: Error()
          }), itemcfg) : itemcfg).getDataByIndex(parseInt(gift.giftId));
          this.loadSp(`icon/item/${giftDataCfg.res}/spriteFrame`, giftIcon);
          giftCount.string = `${gift.giftNum}`;
        }

        onUpdateProps() {
          this.updateLingqi();
        }
        /**
         * 移动红框中的4个小球到黄框中对应的坑位
         * 从 OpenNode 下的 icon_elixir_01-04 移动到 OpenBox 下的 icon_elixir_bg_1-4
         * @param duration 动画持续时间，默认0.8秒
         * @param delayBetween 每个小球之间的延迟时间，默认0.1秒
         */


        moveElixirBallsToSlots(duration = 0.8, delayBetween = 0.1) {
          console.log("找到 OpenNode 和 OpenBox 节点，开始移动小球"); // 定义小球和坑位的对应关系

          const ballSlotPairs = [{
            ballName: "icon_elixir_01",
            slotName: "icon_elixir_bg_1"
          }, {
            ballName: "icon_elixir_02",
            slotName: "icon_elixir_bg_2"
          }, {
            ballName: "icon_elixir_03",
            slotName: "icon_elixir_bg_3"
          }, {
            ballName: "icon_elixir_04",
            slotName: "icon_elixir_bg_4"
          }]; // 遍历每个小球和对应的坑位

          ballSlotPairs.forEach((pair, index) => {
            const ballNode = this.ndOpenArea.getChildByName(pair.ballName);
            const slotNode = this.skOpenBlankArea.node.getChildByName(pair.slotName);
            this.moveBallToSlot(ballNode, slotNode, index * delayBetween, duration);
          });
        }
        /**
         * 移动单个小球到指定坑位
         * @param ballNode 小球节点
         * @param slotNode 坑位节点
         * @param delay 延迟时间
         * @param duration 动画持续时间
         */


        moveBallToSlot(ballNode, slotNode, delay = 0, duration = 0.8) {
          // 获取坑位在世界坐标系中的位置
          const slotWorldPos = slotNode.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO); // 将坑位的世界坐标转换为小球父节点的本地坐标

          const targetLocalPos = ballNode.parent.getComponent(UITransform).convertToNodeSpaceAR(slotWorldPos);
          console.log(`准备移动小球 ${ballNode.name} 从 (${ballNode.position.x}, ${ballNode.position.y}) 到 (${targetLocalPos.x}, ${targetLocalPos.y})`); // 使用 tween 动画移动小球

          tween(ballNode).delay(delay).to(duration, {
            position: targetLocalPos
          }, {
            easing: 'cubicOut'
          }).call(() => {
            console.log(`小球 ${ballNode.name} 已移动到坑位 ${slotNode.name}`);
            this._moveNum++;
            this.switchToAfterKaiDing(this._moveNum);
          }).start();
        }

        switchToAfterKaiDing(count) {
          if (count == 1) {
            this.ndBeforeKaiDing.active = false;
            this.ndAfterKaiDing.active = true;
            this.ndZiYangArea.active = false;
            this.ndResultArea.active = true;
            this._luziNoot.active = false;
          }

          if (count == 4) {
            for (let i = 0; i < 4; i++) {
              const lineNode = instantiate(this._linePrefab);
              this.ndLines.addChild(lineNode);
              const itemNode = instantiate(this._itemPrefab);
              this.ndItems.addChild(itemNode);
              const dataList = (_crd && elixirUnsealCfg === void 0 ? (_reportPossibleCrUseOfelixirUnsealCfg({
                error: Error()
              }), elixirUnsealCfg) : elixirUnsealCfg).getData((_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).game.getEMapType(), i);

              for (let j = 0; j < dataList.length; j++) {
                const giftNode = instantiate(this._giftPrefab);
                itemNode.addChild(giftNode);
              }
            }
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "pbItem", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "ndElixirArea", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "labTimer", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "labLingqi", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "ndBeforeKaiDing", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "ndAfterKaiDing", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "ndZiYangArea", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "ndResultArea", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "ndOpenArea", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "skOpenBlankArea", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "ndLines", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class2.prototype, "ndItems", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class2.prototype, "skeLuzi", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1a8da476ac237c93876713294f8da0abb326d36e.js.map