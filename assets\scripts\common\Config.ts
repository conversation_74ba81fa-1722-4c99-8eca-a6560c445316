import { game, JsonAsset } from "cc";
import { resLoader } from "../loader/ResLoader";
import { oops } from "../core/Oop";
import { GameConfig } from "./GameConfig";
import { GameQueryConfig } from "./GameQueryConfig";
import { GamePublicConfig } from "./GamePublicConfig";
import { GameNet } from "../net/GameNet";
import { uiConfigData } from "./GameUIConfig";
/** 游戏配置静态访问类 */
export class Config {

    /** 配置数据，版本号、支持语种等数据 */
    public game!: GameConfig;

    /** 处理浏览器地址栏参数，包括服务器ip、端口等数据 */
    public query!: GameQueryConfig;


    public gameNet!: GameNet;

    public configList: string[] = [];
    public init(callback: Function) {
        let config_name = "config/config";

        resLoader.load(config_name, JsonAsset, () => {
            var config = resLoader.get(config_name);
            this.query = new GameQueryConfig();
            this.game = new GameConfig(config);
            // 初始化每秒传输帧数
            game.frameRate = this.game.frameRate;
            // Http 服务器地址
            // oops.http.server = this.game.httpServer;
            //  Http 请求超时时间
            // oops.http.timeout = this.game.httpTimeout;

            // 初始化本地存储加密
            oops.storage.init(this.game.localDataKey, this.game.localDataIv);
            // 初始化界面窗口配置
            oops.gui.init(uiConfigData());
            this.initConfigList()
            callback();
        })
    }
    public initConfigList() {
        this.configList = [
            'itembuy',
            'common',
            'map',
            'monster',
            'item',
            'lotterybuy',
            'bossreward',
            'cultivate',
            'getdesc',
            'cultivatelevel',
            'magicboyactivity',
            'exchangeitem',
            'elixir_unseal',
        ]
    }
}

export const enum xlsxName {
    itembuy = "ItemBuy",
    common = "Common",
    map = "Map",
    monster = "Monster",
    item = "Item",
    lotterybuy = "BotteryBuy",
    bossreward = "BossReward",
    cultivate = 'Cultivate',
    getdesc = 'ItemGetDesc',
    cultivatelevel = 'CultivateLevel',
    magicboyactivity = 'MagicBoyActivity',
    exchangeitem = "ExchangeItem",
    elixir_unseal = "ElixirUnseal",
}

export const config = new Config()