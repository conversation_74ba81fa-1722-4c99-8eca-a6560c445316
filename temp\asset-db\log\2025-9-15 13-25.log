2025-9-15 13:25:26-debug: asset-db:worker-init (3037ms)
2025-9-15 13:25:26-debug: Preimport db internal success
2025-9-15 13:25:28-debug: Preimport db assets success
2025-9-15 13:25:28-debug: asset-db:worker-effect-data-processing (459ms)
2025-9-15 13:25:41-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-15 13:25:41-debug: Start up the 'internal' database...
2025-9-15 13:25:42-debug: asset-db:worker-init-script (49ms)
2025-9-15 13:25:42-debug: start asset-db with asset: 419
2025-9-15 13:25:42-debug: Start up the 'assets' database...
2025-9-15 13:25:46-debug: asset-db:worker-startup-database[internal] (17425ms)
2025-9-15 13:25:46-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\demo.scene
background: #aaff85; color: #000;
color: #000;
2025-9-15 13:25:47-debug: start asset-db with asset: 1135
2025-9-15 13:25:47-debug: Asset DB is ready!
2025-9-15 13:25:47-debug: asset-db:worker-startup-database[assets] (20856ms)
2025-9-15 13:25:47-debug: init worker message success
2025-9-15 13:25:48-debug: builder:worker-init (1463ms)
2025-9-15 13:25:49-debug: programming:execute-script (11ms)
2025-9-15 13:35:14-debug: refresh db internal success
2025-9-15 13:35:15-debug: asset-db:worker-effect-data-processing (152ms)
2025-9-15 13:35:15-debug: refresh db assets success
2025-9-15 13:35:15-debug: asset-db:refresh-all-database (376ms)
2025-9-15 13:35:54-debug: refresh db internal success
2025-9-15 13:35:55-debug: asset-db:worker-effect-data-processing (158ms)
2025-9-15 13:35:55-debug: refresh db assets success
2025-9-15 13:35:55-debug: asset-db:refresh-all-database (331ms)
2025-9-15 13:35:56-debug: Query all assets info in project
2025-9-15 13:35:56-debug: BuildAssetLibrary query-assets with assets 3076
2025-9-15 13:35:56-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 13:35:56-debug:   Number of all scripts: 234
2025-9-15 13:35:56-debug:   Number of all scenes: 2
2025-9-15 13:35:56-debug:   Number of other assets: 2695
2025-9-15 13:35:56-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 13:35:56-debug: 查询 Asset Bundle start0%
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 199.76MB rss 562.38MB
2025-9-15 13:35:56-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-9-15 13:35:56-debug: run build task 查询 Asset Bundle success in 18 ms √3%
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 202.41MB rss 562.73MB
2025-9-15 13:35:56-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 13:35:56-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 13:35:56-debug: init bundle assets
2025-9-15 13:35:56-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 13:35:56-debug: Number of scenes: 1
2025-9-15 13:35:56-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 13:35:56-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 13:35:56-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 13:35:56-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 203.51MB rss 563.64MB
2025-9-15 13:35:56-debug: sort script group...
2025-9-15 13:35:56-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-15 13:35:56-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-15 13:35:56-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 13:35:56-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 203.67MB rss 563.98MB
2025-9-15 13:35:56-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 13:35:56-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 13:35:56-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 13:35:56-debug: builder.tasks.settings.macro start11%
2025-9-15 13:35:56-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 203.74MB rss 564.99MB
2025-9-15 13:35:56-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-15 13:35:56-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-15 13:35:56-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 13:35:56-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 203.78MB rss 565.09MB
2025-9-15 13:35:56-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (12ms)
2025-9-15 13:35:56-debug: run build task 整理部分构建选项内数据到 settings.json success in 12 ms √12%
2025-9-15 13:35:56-debug: // ---- build task custom joint texture layouts ----
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 203.89MB rss 562.96MB
2025-9-15 13:35:56-debug: custom joint texture layouts start12%
2025-9-15 13:35:56-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 13:35:56-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 13:35:56-debug: // ---- build task custom joint physics ----
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 203.93MB rss 562.85MB
2025-9-15 13:35:56-debug: custom joint physics start12%
2025-9-15 13:35:56-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 13:35:56-debug: 填充脚本数据到 settings.json start13%
2025-9-15 13:35:56-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 203.95MB rss 562.86MB
2025-9-15 13:35:56-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 13:35:56-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 13:35:56-debug: 填充场景数据到 settings.json start13%
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 203.98MB rss 562.83MB
2025-9-15 13:35:56-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 13:35:56-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 13:35:56-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 13:35:56-debug: Process: heapTotal 225.80MB heapUsed 204.00MB rss 562.83MB
2025-9-15 13:35:57-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (5ms)
2025-9-15 13:35:57-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 5 ms √14%
2025-9-15 13:35:57-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 13:35:57-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 13:35:57-debug: options.md5Cache is false.
2025-9-15 13:35:57-debug: Process: heapTotal 225.80MB heapUsed 204.82MB rss 563.46MB
2025-9-15 13:35:57-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 13:41:11-debug: refresh db internal success
2025-9-15 13:41:11-debug: asset-db:worker-effect-data-processing (154ms)
2025-9-15 13:41:11-debug: refresh db assets success
2025-9-15 13:41:11-debug: asset-db:refresh-all-database (324ms)
2025-9-15 13:41:26-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirNode.prefab...
2025-9-15 13:41:26-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirNode.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 13:41:26-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 13:41:29-debug: refresh db internal success
2025-9-15 13:41:29-debug: asset-db:worker-effect-data-processing (143ms)
2025-9-15 13:41:29-debug: refresh db assets success
2025-9-15 13:41:29-debug: asset-db:refresh-all-database (317ms)
2025-9-15 13:41:30-debug: Query all assets info in project
2025-9-15 13:41:30-debug: BuildAssetLibrary query-assets with assets 3076
2025-9-15 13:41:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 13:41:30-debug:   Number of all scenes: 2
2025-9-15 13:41:30-debug:   Number of all scripts: 234
2025-9-15 13:41:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 202.44MB rss 561.79MB
2025-9-15 13:41:30-debug:   Number of other assets: 2695
2025-9-15 13:41:30-debug: 查询 Asset Bundle start0%
2025-9-15 13:41:30-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-9-15 13:41:30-debug: run build task 查询 Asset Bundle success in 17 ms √3%
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 198.13MB rss 561.61MB
2025-9-15 13:41:30-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 13:41:30-debug: init bundle assets
2025-9-15 13:41:30-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 13:41:30-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 13:41:30-debug: Number of scenes: 1
2025-9-15 13:41:30-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 13:41:30-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 13:41:30-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 199.13MB rss 561.90MB
2025-9-15 13:41:30-debug: sort script group...
2025-9-15 13:41:30-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 13:41:30-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 13:41:30-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 13:41:30-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 13:41:30-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 199.29MB rss 561.84MB
2025-9-15 13:41:30-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 13:41:30-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 13:41:30-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 13:41:30-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 199.36MB rss 561.60MB
2025-9-15 13:41:30-debug: builder.tasks.settings.macro start11%
2025-9-15 13:41:30-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 13:41:30-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 13:41:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 199.40MB rss 561.60MB
2025-9-15 13:41:30-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 13:41:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 13:41:30-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 13:41:30-debug: custom joint texture layouts start12%
2025-9-15 13:41:30-debug: // ---- build task custom joint texture layouts ----
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 199.47MB rss 561.63MB
2025-9-15 13:41:30-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 13:41:30-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 199.50MB rss 561.64MB
2025-9-15 13:41:30-debug: custom joint physics start12%
2025-9-15 13:41:30-debug: // ---- build task custom joint physics ----
2025-9-15 13:41:30-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 13:41:30-debug: 填充脚本数据到 settings.json start13%
2025-9-15 13:41:30-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 13:41:30-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 199.53MB rss 561.64MB
2025-9-15 13:41:30-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 13:41:30-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 13:41:30-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 13:41:30-debug: 填充场景数据到 settings.json start13%
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 199.57MB rss 561.64MB
2025-9-15 13:41:30-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 13:41:30-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 13:41:30-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 199.59MB rss 561.64MB
2025-9-15 13:41:30-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-15 13:41:30-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-15 13:41:30-debug: Process: heapTotal 225.02MB heapUsed 200.44MB rss 562.49MB
2025-9-15 13:41:30-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 13:41:30-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 13:41:30-debug: options.md5Cache is false.
2025-9-15 13:41:30-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 13:41:30-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 13:45:10-debug: refresh db internal success
2025-9-15 13:45:10-debug: asset-db:worker-effect-data-processing (147ms)
2025-9-15 13:45:10-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 13:45:10-debug: refresh db assets success
2025-9-15 13:45:10-debug: asset-db:refresh-all-database (321ms)
2025-9-15 13:45:11-debug: Query all assets info in project
2025-9-15 13:45:11-debug: BuildAssetLibrary query-assets with assets 3076
2025-9-15 13:45:11-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 13:45:11-debug:   Number of other assets: 2695
2025-9-15 13:45:11-debug: 查询 Asset Bundle start0%
2025-9-15 13:45:11-debug:   Number of all scripts: 234
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 200.03MB rss 563.43MB
2025-9-15 13:45:11-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 13:45:11-debug:   Number of all scenes: 2
2025-9-15 13:45:11-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-9-15 13:45:11-debug: run build task 查询 Asset Bundle success in 18 ms √3%
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 202.69MB rss 563.87MB
2025-9-15 13:45:11-debug: init bundle assets
2025-9-15 13:45:11-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 13:45:11-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 13:45:11-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 13:45:11-debug: Number of scenes: 1
2025-9-15 13:45:11-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 13:45:11-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 13:45:11-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 13:45:11-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 203.69MB rss 564.04MB
2025-9-15 13:45:11-debug: sort script group...
2025-9-15 13:45:11-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 13:45:11-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 203.84MB rss 564.01MB
2025-9-15 13:45:11-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 13:45:11-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 13:45:11-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 13:45:11-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 13:45:11-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 13:45:11-debug: builder.tasks.settings.macro start11%
2025-9-15 13:45:11-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 203.92MB rss 563.91MB
2025-9-15 13:45:11-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 13:45:11-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 13:45:11-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 13:45:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 203.95MB rss 563.84MB
2025-9-15 13:45:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 13:45:11-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 204.03MB rss 563.86MB
2025-9-15 13:45:11-debug: custom joint texture layouts start12%
2025-9-15 13:45:11-debug: // ---- build task custom joint texture layouts ----
2025-9-15 13:45:11-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 13:45:11-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 13:45:11-debug: custom joint physics start12%
2025-9-15 13:45:11-debug: // ---- build task custom joint physics ----
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 204.06MB rss 563.86MB
2025-9-15 13:45:11-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 13:45:11-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 13:45:11-debug: 填充脚本数据到 settings.json start13%
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 204.09MB rss 563.87MB
2025-9-15 13:45:11-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 13:45:11-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 204.12MB rss 563.87MB
2025-9-15 13:45:11-debug: 填充场景数据到 settings.json start13%
2025-9-15 13:45:11-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 13:45:11-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 13:45:11-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 204.14MB rss 563.87MB
2025-9-15 13:45:11-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 13:45:11-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-15 13:45:11-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-15 13:45:11-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 13:45:11-debug: Process: heapTotal 226.01MB heapUsed 204.97MB rss 564.67MB
2025-9-15 13:45:11-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 13:45:11-debug: options.md5Cache is false.
2025-9-15 13:45:11-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 14:42:15-debug: refresh db internal success
2025-9-15 14:42:15-debug: asset-db:worker-effect-data-processing (189ms)
2025-9-15 14:42:15-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\net\GameNet.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 14:42:15-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 14:42:15-debug: refresh db assets success
2025-9-15 14:42:15-debug: asset-db:refresh-all-database (393ms)
2025-9-15 14:42:16-debug: Query all assets info in project
2025-9-15 14:42:16-debug: BuildAssetLibrary query-assets with assets 3076
2025-9-15 14:42:16-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 14:42:16-debug:   Number of all scenes: 2
2025-9-15 14:42:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 14:42:16-debug:   Number of all scripts: 234
2025-9-15 14:42:16-debug:   Number of other assets: 2695
2025-9-15 14:42:16-debug: 查询 Asset Bundle start0%
2025-9-15 14:42:16-debug: Process: heapTotal 224.13MB heapUsed 198.59MB rss 562.87MB
2025-9-15 14:42:16-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-9-15 14:42:16-debug: run build task 查询 Asset Bundle success in 17 ms √3%
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 201.87MB rss 562.01MB
2025-9-15 14:42:16-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 14:42:16-debug: init bundle assets
2025-9-15 14:42:16-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 14:42:16-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 14:42:16-debug: Number of scenes: 1
2025-9-15 14:42:16-debug: run build task 查询使用的资源以及资源包配置 success in 5 ms √7%
2025-9-15 14:42:16-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (5ms)
2025-9-15 14:42:16-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 202.87MB rss 562.17MB
2025-9-15 14:42:16-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 14:42:16-debug: sort script group...
2025-9-15 14:42:16-debug: run build task 整理脚本分组与脚本数据 success in 0 ms √10%
2025-9-15 14:42:16-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 14:42:16-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 203.02MB rss 562.15MB
2025-9-15 14:42:16-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 14:42:16-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 14:42:16-debug: builder.tasks.settings.macro start11%
2025-9-15 14:42:16-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 14:42:16-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 203.09MB rss 562.07MB
2025-9-15 14:42:16-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-15 14:42:16-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-15 14:42:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 14:42:16-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 203.13MB rss 561.96MB
2025-9-15 14:42:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-15 14:42:16-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-15 14:42:16-debug: // ---- build task custom joint texture layouts ----
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 203.20MB rss 562.00MB
2025-9-15 14:42:16-debug: custom joint texture layouts start12%
2025-9-15 14:42:16-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 14:42:16-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 14:42:16-debug: // ---- build task custom joint physics ----
2025-9-15 14:42:16-debug: custom joint physics start12%
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 203.23MB rss 562.01MB
2025-9-15 14:42:16-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 14:42:16-debug: 填充脚本数据到 settings.json start13%
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 203.26MB rss 562.01MB
2025-9-15 14:42:16-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 14:42:16-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 14:42:16-debug: 填充场景数据到 settings.json start13%
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 203.29MB rss 562.00MB
2025-9-15 14:42:16-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 14:42:16-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 14:42:16-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 14:42:16-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 203.31MB rss 562.00MB
2025-9-15 14:42:16-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 5 ms √14%
2025-9-15 14:42:16-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 14:42:16-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (5ms)
2025-9-15 14:42:16-debug: options.md5Cache is false.
2025-9-15 14:42:16-debug: Process: heapTotal 226.13MB heapUsed 204.13MB rss 562.95MB
2025-9-15 14:42:16-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 14:42:16-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 14:42:16-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 14:44:03-debug: refresh db internal success
2025-9-15 14:44:03-debug: asset-db:worker-effect-data-processing (177ms)
2025-9-15 14:44:03-debug: refresh db assets success
2025-9-15 14:44:03-debug: asset-db:refresh-all-database (367ms)
2025-9-15 14:45:19-debug: refresh db internal success
2025-9-15 14:45:19-debug: asset-db:worker-effect-data-processing (148ms)
2025-9-15 14:45:19-debug: refresh db assets success
2025-9-15 14:45:19-debug: asset-db:refresh-all-database (309ms)
2025-9-15 14:46:25-debug: refresh db internal success
2025-9-15 14:46:25-debug: asset-db:worker-effect-data-processing (173ms)
2025-9-15 14:46:25-debug: refresh db assets success
2025-9-15 14:46:25-debug: asset-db:refresh-all-database (404ms)
2025-9-15 14:46:26-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\bg_luzi.png...
2025-9-15 14:46:26-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\bg_luzi.png
background: #aaff85; color: #000;
color: #000;
2025-9-15 14:46:26-debug: refresh db internal success
2025-9-15 14:46:26-debug: %cReImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\bg_luzi.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-15 14:46:26-debug: %cReImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\bg_luzi.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-15 14:46:26-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir success
2025-9-15 14:46:27-debug: asset-db:worker-effect-data-processing (173ms)
2025-9-15 14:46:27-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir
background: #aaff85; color: #000;
color: #000;
2025-9-15 14:46:27-debug: refresh db assets success
2025-9-15 14:46:27-debug: asset-db:refresh-all-database (490ms)
2025-9-15 14:47:35-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-15 14:47:35-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 14:47:35-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 14:47:36-debug: Query all assets info in project
2025-9-15 14:47:36-debug: BuildAssetLibrary query-assets with assets 3076
2025-9-15 14:47:36-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 14:47:36-debug:   Number of all scenes: 2
2025-9-15 14:47:36-debug:   Number of all scripts: 234
2025-9-15 14:47:36-debug:   Number of other assets: 2695
2025-9-15 14:47:36-debug: 查询 Asset Bundle start0%
2025-9-15 14:47:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 14:47:36-debug: Process: heapTotal 216.67MB heapUsed 206.38MB rss 575.33MB
2025-9-15 14:47:36-debug: // ---- build task 查询 Asset Bundle ---- (50ms)
2025-9-15 14:47:36-debug: run build task 查询 Asset Bundle success in 50 ms √3%
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 194.59MB rss 550.38MB
2025-9-15 14:47:36-debug: init bundle assets
2025-9-15 14:47:36-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 14:47:36-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 14:47:36-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 14:47:36-debug: Number of scenes: 1
2025-9-15 14:47:36-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (8ms)
2025-9-15 14:47:36-debug: run build task 查询使用的资源以及资源包配置 success in 8 ms √7%
2025-9-15 14:47:36-debug: sort script group...
2025-9-15 14:47:36-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 14:47:36-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 195.58MB rss 549.83MB
2025-9-15 14:47:36-debug: run build task 整理脚本分组与脚本数据 success in 0 ms √10%
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 195.73MB rss 549.81MB
2025-9-15 14:47:36-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 14:47:36-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 14:47:36-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 14:47:36-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 14:47:36-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 14:47:36-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 195.81MB rss 549.73MB
2025-9-15 14:47:36-debug: builder.tasks.settings.macro start11%
2025-9-15 14:47:36-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 14:47:36-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 14:47:36-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 14:47:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 195.84MB rss 549.63MB
2025-9-15 14:47:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-15 14:47:36-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms √12%
2025-9-15 14:47:36-debug: // ---- build task custom joint texture layouts ----
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 195.92MB rss 549.67MB
2025-9-15 14:47:36-debug: custom joint texture layouts start12%
2025-9-15 14:47:36-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 14:47:36-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 14:47:36-debug: // ---- build task custom joint physics ----
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 195.95MB rss 549.67MB
2025-9-15 14:47:36-debug: custom joint physics start12%
2025-9-15 14:47:36-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 14:47:36-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 195.98MB rss 549.67MB
2025-9-15 14:47:36-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 14:47:36-debug: 填充脚本数据到 settings.json start13%
2025-9-15 14:47:36-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 14:47:36-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 14:47:36-debug: 填充场景数据到 settings.json start13%
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 196.01MB rss 549.67MB
2025-9-15 14:47:36-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 14:47:36-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 14:47:36-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 14:47:36-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 196.04MB rss 549.67MB
2025-9-15 14:47:36-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (4ms)
2025-9-15 14:47:36-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 4 ms √14%
2025-9-15 14:47:36-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 14:47:36-debug: Process: heapTotal 217.09MB heapUsed 196.78MB rss 550.77MB
2025-9-15 14:47:36-debug: options.md5Cache is false.
2025-9-15 14:47:36-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 14:47:36-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 14:47:36-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 14:52:20-debug: refresh db internal success
2025-9-15 14:52:20-debug: asset-db:worker-effect-data-processing (206ms)
2025-9-15 14:52:20-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\net\GameNet.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 14:52:21-debug: refresh db assets success
2025-9-15 14:52:21-debug: asset-db:refresh-all-database (595ms)
2025-9-15 14:52:25-debug: Query all assets info in project
2025-9-15 14:52:25-debug: BuildAssetLibrary query-assets with assets 3076
2025-9-15 14:52:25-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 14:52:25-debug:   Number of all scenes: 2
2025-9-15 14:52:25-debug:   Number of all scripts: 234
2025-9-15 14:52:25-debug:   Number of other assets: 2695
2025-9-15 14:52:25-debug: 查询 Asset Bundle start0%
2025-9-15 14:52:25-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 14:52:25-debug: Process: heapTotal 229.80MB heapUsed 210.81MB rss 569.99MB
2025-9-15 14:52:25-debug: // ---- build task 查询 Asset Bundle ---- (37ms)
2025-9-15 14:52:25-debug: run build task 查询 Asset Bundle success in 37 ms √3%
2025-9-15 14:52:25-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 14:52:25-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 14:52:25-debug: Process: heapTotal 230.05MB heapUsed 209.10MB rss 569.40MB
2025-9-15 14:52:25-debug: init bundle assets
2025-9-15 14:52:25-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 14:52:25-debug: Number of scenes: 1
2025-9-15 14:52:25-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (9ms)
2025-9-15 14:52:25-debug: run build task 查询使用的资源以及资源包配置 success in 9 ms √7%
2025-9-15 14:52:25-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 14:52:25-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 14:52:25-debug: Process: heapTotal 230.05MB heapUsed 210.07MB rss 569.32MB
2025-9-15 14:52:25-debug: sort script group...
2025-9-15 14:52:25-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-15 14:52:25-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-15 14:52:25-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 14:52:25-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 14:52:25-debug: Process: heapTotal 230.05MB heapUsed 210.22MB rss 569.27MB
2025-9-15 14:52:25-debug: builder.tasks.settings.macro start11%
2025-9-15 14:52:25-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 14:52:25-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 14:52:25-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 14:52:25-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 14:52:25-debug: Process: heapTotal 230.05MB heapUsed 210.30MB rss 569.13MB
2025-9-15 14:52:25-debug: // ---- build task builder.tasks.settings.macro ---- (3ms)
2025-9-15 14:52:25-debug: run build task builder.tasks.settings.macro success in 3 ms √11%
2025-9-15 14:52:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 14:52:25-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 14:52:25-debug: Process: heapTotal 230.05MB heapUsed 210.34MB rss 569.13MB
2025-9-15 14:52:26-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (11ms)
2025-9-15 14:52:26-debug: run build task 整理部分构建选项内数据到 settings.json success in 11 ms √12%
2025-9-15 14:52:26-debug: custom joint texture layouts start12%
2025-9-15 14:52:26-debug: // ---- build task custom joint texture layouts ----
2025-9-15 14:52:26-debug: Process: heapTotal 230.05MB heapUsed 210.41MB rss 569.16MB
2025-9-15 14:52:26-debug: // ---- build task custom joint texture layouts ---- (5ms)
2025-9-15 14:52:26-debug: run build task custom joint texture layouts success in 5 ms √12%
2025-9-15 14:52:26-debug: custom joint physics start12%
2025-9-15 14:52:26-debug: // ---- build task custom joint physics ----
2025-9-15 14:52:26-debug: Process: heapTotal 230.05MB heapUsed 210.45MB rss 569.17MB
2025-9-15 14:52:26-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 14:52:26-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 14:52:26-debug: 填充脚本数据到 settings.json start13%
2025-9-15 14:52:26-debug: Process: heapTotal 230.05MB heapUsed 210.48MB rss 569.17MB
2025-9-15 14:52:26-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 14:52:26-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 14:52:26-debug: 填充场景数据到 settings.json start13%
2025-9-15 14:52:26-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 14:52:26-debug: // ---- build task 填充场景数据到 settings.json ---- (4ms)
2025-9-15 14:52:26-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 14:52:26-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 14:52:26-debug: Process: heapTotal 230.05MB heapUsed 210.50MB rss 569.17MB
2025-9-15 14:52:26-debug: Process: heapTotal 230.05MB heapUsed 210.53MB rss 569.17MB
2025-9-15 14:52:26-debug: run build task 填充场景数据到 settings.json success in 4 ms √14%
2025-9-15 14:52:26-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-15 14:52:26-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-15 14:52:26-debug: options.md5Cache is false.
2025-9-15 14:52:26-debug: Process: heapTotal 230.05MB heapUsed 211.23MB rss 569.19MB
2025-9-15 14:52:26-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 14:52:26-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 14:52:26-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 14:52:26-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 14:57:30-debug: Query all assets info in project
2025-9-15 14:57:30-debug: BuildAssetLibrary query-assets with assets 3076
2025-9-15 14:57:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 14:57:30-debug:   Number of all scenes: 2
2025-9-15 14:57:30-debug:   Number of all scripts: 234
2025-9-15 14:57:30-debug:   Number of other assets: 2695
2025-9-15 14:57:30-debug: 查询 Asset Bundle start0%
2025-9-15 14:57:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 14:57:30-debug: Process: heapTotal 217.05MB heapUsed 208.15MB rss 556.04MB
2025-9-15 14:57:30-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-9-15 14:57:30-debug: run build task 查询 Asset Bundle success in 30 ms √3%
2025-9-15 14:57:30-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 14:57:30-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 14:57:30-debug: Process: heapTotal 220.55MB heapUsed 209.30MB rss 559.13MB
2025-9-15 14:57:30-debug: init bundle assets
2025-9-15 14:57:30-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 14:57:30-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (8ms)
2025-9-15 14:57:30-debug: run build task 查询使用的资源以及资源包配置 success in 8 ms √7%
2025-9-15 14:57:30-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 14:57:30-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 14:57:30-debug: Process: heapTotal 220.55MB heapUsed 210.51MB rss 559.23MB
2025-9-15 14:57:30-debug: sort script group...
2025-9-15 14:57:30-debug: Number of scenes: 1
2025-9-15 14:57:30-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-15 14:57:30-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-15 14:57:30-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 14:57:30-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 14:57:30-debug: Process: heapTotal 220.55MB heapUsed 210.66MB rss 559.22MB
2025-9-15 14:57:30-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 14:57:30-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 14:57:30-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 14:57:30-debug: builder.tasks.settings.macro start11%
2025-9-15 14:57:30-debug: Process: heapTotal 220.55MB heapUsed 210.74MB rss 559.14MB
2025-9-15 14:57:30-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 14:57:30-debug: // ---- build task builder.tasks.settings.macro ---- (3ms)
2025-9-15 14:57:30-debug: run build task builder.tasks.settings.macro success in 3 ms √11%
2025-9-15 14:57:30-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 14:57:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 14:57:30-debug: Process: heapTotal 220.55MB heapUsed 210.77MB rss 559.05MB
2025-9-15 14:57:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-15 14:57:30-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-15 14:57:30-debug: custom joint texture layouts start12%
2025-9-15 14:57:30-debug: // ---- build task custom joint texture layouts ----
2025-9-15 14:57:30-debug: Process: heapTotal 220.55MB heapUsed 210.84MB rss 559.05MB
2025-9-15 14:57:30-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 14:57:30-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 14:57:30-debug: custom joint physics start12%
2025-9-15 14:57:30-debug: // ---- build task custom joint physics ----
2025-9-15 14:57:30-debug: Process: heapTotal 220.55MB heapUsed 210.87MB rss 559.05MB
2025-9-15 14:57:30-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 14:57:30-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 14:57:30-debug: 填充脚本数据到 settings.json start13%
2025-9-15 14:57:30-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 14:57:30-debug: Process: heapTotal 220.55MB heapUsed 210.90MB rss 559.05MB
2025-9-15 14:57:30-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 14:57:30-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 14:57:30-debug: 填充场景数据到 settings.json start13%
2025-9-15 14:57:30-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 14:57:30-debug: Process: heapTotal 220.55MB heapUsed 210.94MB rss 559.06MB
2025-9-15 14:57:30-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 14:57:30-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 14:57:30-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 14:57:30-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 14:57:30-debug: Process: heapTotal 220.55MB heapUsed 210.97MB rss 559.07MB
2025-9-15 14:57:30-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-15 14:57:30-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-15 14:57:30-debug: options.md5Cache is false.
2025-9-15 14:57:30-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 14:57:30-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 14:57:30-debug: Process: heapTotal 224.80MB heapUsed 210.26MB rss 559.19MB
2025-9-15 14:57:30-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 14:57:30-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 14:59:24-debug: Query all assets info in project
2025-9-15 14:59:24-debug: BuildAssetLibrary query-assets with assets 3076
2025-9-15 14:59:24-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 14:59:25-debug:   Number of all scenes: 2
2025-9-15 14:59:25-debug:   Number of all scripts: 234
2025-9-15 14:59:25-debug:   Number of other assets: 2695
2025-9-15 14:59:25-debug: 查询 Asset Bundle start0%
2025-9-15 14:59:25-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 14:59:25-debug: Process: heapTotal 219.55MB heapUsed 212.03MB rss 557.83MB
2025-9-15 14:59:25-debug: // ---- build task 查询 Asset Bundle ---- (34ms)
2025-9-15 14:59:25-debug: run build task 查询 Asset Bundle success in 34 ms √3%
2025-9-15 14:59:25-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 14:59:25-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 213.75MB rss 562.70MB
2025-9-15 14:59:25-debug: init bundle assets
2025-9-15 14:59:25-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 14:59:25-debug: Number of scenes: 1
2025-9-15 14:59:25-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (16ms)
2025-9-15 14:59:25-debug: run build task 查询使用的资源以及资源包配置 success in 16 ms √7%
2025-9-15 14:59:25-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 14:59:25-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 213.39MB rss 564.23MB
2025-9-15 14:59:25-debug: sort script group...
2025-9-15 14:59:25-debug: // ---- build task 整理脚本分组与脚本数据 ---- (4ms)
2025-9-15 14:59:25-debug: run build task 整理脚本分组与脚本数据 success in 4 ms √10%
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 213.54MB rss 564.82MB
2025-9-15 14:59:25-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 14:59:25-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 14:59:25-debug: // ---- build task 初始化 settings.json 与 config.json ---- (3ms)
2025-9-15 14:59:25-debug: run build task 初始化 settings.json 与 config.json success in 3 ms √11%
2025-9-15 14:59:25-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 14:59:25-debug: builder.tasks.settings.macro start11%
2025-9-15 14:59:25-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 213.62MB rss 563.73MB
2025-9-15 14:59:25-debug: // ---- build task builder.tasks.settings.macro ---- (6ms)
2025-9-15 14:59:25-debug: run build task builder.tasks.settings.macro success in 6 ms √11%
2025-9-15 14:59:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 213.65MB rss 564.78MB
2025-9-15 14:59:25-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 14:59:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-9-15 14:59:25-debug: run build task 整理部分构建选项内数据到 settings.json success in 6 ms √12%
2025-9-15 14:59:25-debug: custom joint texture layouts start12%
2025-9-15 14:59:25-debug: // ---- build task custom joint texture layouts ----
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 213.72MB rss 562.80MB
2025-9-15 14:59:25-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 14:59:25-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 14:59:25-debug: custom joint physics start12%
2025-9-15 14:59:25-debug: // ---- build task custom joint physics ----
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 213.75MB rss 562.65MB
2025-9-15 14:59:25-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 14:59:25-debug: 填充脚本数据到 settings.json start13%
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 213.78MB rss 562.56MB
2025-9-15 14:59:25-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 14:59:25-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 14:59:25-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 14:59:25-debug: 填充场景数据到 settings.json start13%
2025-9-15 14:59:25-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 213.81MB rss 562.46MB
2025-9-15 14:59:25-debug: run build task 填充场景数据到 settings.json success in 2 ms √14%
2025-9-15 14:59:25-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 213.84MB rss 562.39MB
2025-9-15 14:59:25-debug: // ---- build task 填充场景数据到 settings.json ---- (2ms)
2025-9-15 14:59:25-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 14:59:25-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (3ms)
2025-9-15 14:59:25-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 3 ms √14%
2025-9-15 14:59:25-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 14:59:25-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 14:59:25-debug: Process: heapTotal 224.30MB heapUsed 214.57MB rss 562.43MB
2025-9-15 14:59:25-debug: options.md5Cache is false.
2025-9-15 14:59:25-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 14:59:25-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 15:01:24-debug: refresh db internal success
2025-9-15 15:01:25-debug: asset-db:worker-effect-data-processing (256ms)
2025-9-15 15:01:25-debug: refresh db assets success
2025-9-15 15:01:25-debug: asset-db:refresh-all-database (621ms)
2025-9-15 15:02:12-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\elixir_icon_bg.png...
2025-9-15 15:02:12-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\elixir_icon_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:02:12-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\elixir_icon_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:02:12-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\elixir_icon_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:02:12-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir success
2025-9-15 15:02:12-debug: refresh db internal success
2025-9-15 15:02:12-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:02:13-debug: asset-db:worker-effect-data-processing (181ms)
2025-9-15 15:02:13-debug: refresh db assets success
2025-9-15 15:02:13-debug: asset-db:refresh-all-database (390ms)
2025-9-15 15:02:34-debug: refresh db internal success
2025-9-15 15:02:34-debug: asset-db:worker-effect-data-processing (177ms)
2025-9-15 15:02:34-debug: refresh db assets success
2025-9-15 15:02:34-debug: asset-db:refresh-all-database (369ms)
2025-9-15 15:02:35-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\bt-around.png...
2025-9-15 15:02:35-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\bt-around.png
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:02:35-debug: refresh db internal success
2025-9-15 15:02:35-debug: %cReImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\bt-around.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:02:35-debug: %cReImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir\bt-around.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:02:35-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir success
2025-9-15 15:02:35-debug: asset-db:worker-effect-data-processing (134ms)
2025-9-15 15:02:35-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\image\popup\v3\elixir
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:02:35-debug: refresh db assets success
2025-9-15 15:02:35-debug: asset-db:refresh-all-database (389ms)
2025-9-15 15:03:20-debug: refresh db internal success
2025-9-15 15:03:21-debug: asset-db:worker-effect-data-processing (141ms)
2025-9-15 15:03:21-debug: refresh db assets success
2025-9-15 15:03:21-debug: asset-db:refresh-all-database (310ms)
2025-9-15 15:13:09-debug: refresh db internal success
2025-9-15 15:13:09-debug: asset-db:worker-effect-data-processing (178ms)
2025-9-15 15:13:09-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:13:09-debug: refresh db assets success
2025-9-15 15:13:09-debug: asset-db:refresh-all-database (364ms)
2025-9-15 15:13:10-debug: Query all assets info in project
2025-9-15 15:13:10-debug: BuildAssetLibrary query-assets with assets 3079
2025-9-15 15:13:10-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 15:13:10-debug:   Number of other assets: 2698
2025-9-15 15:13:10-debug:   Number of all scripts: 234
2025-9-15 15:13:10-debug: 查询 Asset Bundle start0%
2025-9-15 15:13:10-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 203.86MB rss 571.70MB
2025-9-15 15:13:10-debug:   Number of all scenes: 2
2025-9-15 15:13:10-debug: // ---- build task 查询 Asset Bundle ---- (15ms)
2025-9-15 15:13:10-debug: run build task 查询 Asset Bundle success in 15 ms √3%
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 199.20MB rss 570.49MB
2025-9-15 15:13:10-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 15:13:10-debug: init bundle assets
2025-9-15 15:13:10-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 15:13:10-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 15:13:10-debug: Number of scenes: 1
2025-9-15 15:13:10-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (7ms)
2025-9-15 15:13:10-debug: run build task 查询使用的资源以及资源包配置 success in 7 ms √7%
2025-9-15 15:13:10-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 15:13:10-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 200.20MB rss 570.63MB
2025-9-15 15:13:10-debug: sort script group...
2025-9-15 15:13:10-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 15:13:10-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 15:13:10-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 200.35MB rss 570.59MB
2025-9-15 15:13:10-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 15:13:10-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 15:13:10-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 15:13:10-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 15:13:10-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 15:13:10-debug: builder.tasks.settings.macro start11%
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 200.43MB rss 570.54MB
2025-9-15 15:13:10-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 15:13:10-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 15:13:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 200.46MB rss 570.41MB
2025-9-15 15:13:10-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 15:13:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 15:13:10-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 15:13:10-debug: // ---- build task custom joint texture layouts ----
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 200.54MB rss 570.44MB
2025-9-15 15:13:10-debug: custom joint texture layouts start12%
2025-9-15 15:13:10-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 15:13:10-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 200.57MB rss 570.45MB
2025-9-15 15:13:10-debug: custom joint physics start12%
2025-9-15 15:13:10-debug: // ---- build task custom joint physics ----
2025-9-15 15:13:10-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 15:13:10-debug: 填充脚本数据到 settings.json start13%
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 200.59MB rss 570.45MB
2025-9-15 15:13:10-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 15:13:10-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 15:13:10-debug: 填充场景数据到 settings.json start13%
2025-9-15 15:13:10-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 200.62MB rss 570.45MB
2025-9-15 15:13:10-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 15:13:10-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 15:13:10-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 15:13:10-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 200.65MB rss 570.45MB
2025-9-15 15:13:10-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (7ms)
2025-9-15 15:13:10-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 15:13:10-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 7 ms √14%
2025-9-15 15:13:10-debug: Process: heapTotal 229.68MB heapUsed 201.51MB rss 571.27MB
2025-9-15 15:13:10-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 15:13:10-debug: options.md5Cache is false.
2025-9-15 15:13:10-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 15:13:10-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 15:14:40-debug: refresh db internal success
2025-9-15 15:14:40-debug: asset-db:worker-effect-data-processing (158ms)
2025-9-15 15:14:40-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:14:40-debug: refresh db assets success
2025-9-15 15:14:40-debug: asset-db:refresh-all-database (345ms)
2025-9-15 15:14:41-debug: Query all assets info in project
2025-9-15 15:14:41-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 15:14:41-debug:   Number of all scenes: 2
2025-9-15 15:14:41-debug: BuildAssetLibrary query-assets with assets 3079
2025-9-15 15:14:41-debug: 查询 Asset Bundle start0%
2025-9-15 15:14:41-debug:   Number of all scripts: 234
2025-9-15 15:14:41-debug:   Number of other assets: 2698
2025-9-15 15:14:41-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 15:14:41-debug: Process: heapTotal 230.33MB heapUsed 201.41MB rss 570.85MB
2025-9-15 15:14:41-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-9-15 15:14:41-debug: run build task 查询 Asset Bundle success in 25 ms √3%
2025-9-15 15:14:41-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 15:14:41-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 15:14:41-debug: Process: heapTotal 230.33MB heapUsed 203.68MB rss 570.43MB
2025-9-15 15:14:41-debug: init bundle assets
2025-9-15 15:14:41-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 15:14:41-debug: Number of scenes: 1
2025-9-15 15:14:41-debug: run build task 查询使用的资源以及资源包配置 success in 8 ms √7%
2025-9-15 15:14:41-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (8ms)
2025-9-15 15:14:41-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 15:14:41-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 15:14:41-debug: sort script group...
2025-9-15 15:14:41-debug: Process: heapTotal 230.58MB heapUsed 198.78MB rss 570.46MB
2025-9-15 15:14:41-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 15:14:41-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 15:14:41-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 15:14:41-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 15:14:41-debug: Process: heapTotal 230.58MB heapUsed 198.94MB rss 570.42MB
2025-9-15 15:14:41-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 15:14:41-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 15:14:41-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 15:14:41-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 15:14:41-debug: Process: heapTotal 230.58MB heapUsed 199.02MB rss 570.30MB
2025-9-15 15:14:41-debug: builder.tasks.settings.macro start11%
2025-9-15 15:14:41-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 15:14:41-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 15:14:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 15:14:41-debug: Process: heapTotal 230.58MB heapUsed 199.06MB rss 570.13MB
2025-9-15 15:14:41-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 15:14:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-15 15:14:41-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms √12%
2025-9-15 15:14:41-debug: Process: heapTotal 230.58MB heapUsed 199.13MB rss 570.16MB
2025-9-15 15:14:41-debug: // ---- build task custom joint texture layouts ----
2025-9-15 15:14:41-debug: custom joint texture layouts start12%
2025-9-15 15:14:41-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 15:14:41-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 15:14:41-debug: Process: heapTotal 230.58MB heapUsed 199.16MB rss 570.16MB
2025-9-15 15:14:41-debug: // ---- build task custom joint physics ----
2025-9-15 15:14:41-debug: custom joint physics start12%
2025-9-15 15:14:41-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 15:14:41-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 15:14:41-debug: Process: heapTotal 230.58MB heapUsed 199.19MB rss 570.16MB
2025-9-15 15:14:41-debug: 填充脚本数据到 settings.json start13%
2025-9-15 15:14:41-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 15:14:41-debug: 填充场景数据到 settings.json start13%
2025-9-15 15:14:41-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 15:14:41-debug: Process: heapTotal 230.58MB heapUsed 199.22MB rss 570.16MB
2025-9-15 15:14:41-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 15:14:41-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 15:14:41-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 15:14:41-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 15:14:41-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 15:14:41-debug: Process: heapTotal 230.58MB heapUsed 199.25MB rss 570.16MB
2025-9-15 15:14:41-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (7ms)
2025-9-15 15:14:41-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 7 ms √14%
2025-9-15 15:14:41-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 15:14:41-debug: Process: heapTotal 230.58MB heapUsed 200.10MB rss 571.14MB
2025-9-15 15:14:41-debug: options.md5Cache is false.
2025-9-15 15:14:41-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 15:14:41-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 15:15:13-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-15 15:15:13-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:15:13-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 15:15:19-debug: refresh db internal success
2025-9-15 15:15:19-debug: asset-db:worker-effect-data-processing (156ms)
2025-9-15 15:15:19-debug: refresh db assets success
2025-9-15 15:15:19-debug: asset-db:refresh-all-database (317ms)
2025-9-15 15:16:28-debug: refresh db internal success
2025-9-15 15:16:28-debug: asset-db:worker-effect-data-processing (149ms)
2025-9-15 15:16:28-debug: refresh db assets success
2025-9-15 15:16:28-debug: asset-db:refresh-all-database (313ms)
2025-9-15 15:16:46-debug: refresh db internal success
2025-9-15 15:16:46-debug: asset-db:worker-effect-data-processing (162ms)
2025-9-15 15:16:46-debug: refresh db assets success
2025-9-15 15:16:46-debug: asset-db:refresh-all-database (321ms)
2025-9-15 15:22:43-debug: refresh db internal success
2025-9-15 15:22:43-debug: asset-db:worker-effect-data-processing (181ms)
2025-9-15 15:22:43-debug: refresh db assets success
2025-9-15 15:22:43-debug: asset-db:refresh-all-database (382ms)
2025-9-15 15:23:33-debug: refresh db internal success
2025-9-15 15:23:33-debug: asset-db:worker-effect-data-processing (161ms)
2025-9-15 15:23:33-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:23:33-debug: refresh db assets success
2025-9-15 15:23:33-debug: asset-db:refresh-all-database (358ms)
2025-9-15 15:23:34-debug: Query all assets info in project
2025-9-15 15:23:34-debug: BuildAssetLibrary query-assets with assets 3079
2025-9-15 15:23:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 15:23:34-debug:   Number of other assets: 2698
2025-9-15 15:23:34-debug:   Number of all scenes: 2
2025-9-15 15:23:34-debug: 查询 Asset Bundle start0%
2025-9-15 15:23:34-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 15:23:34-debug: Process: heapTotal 230.30MB heapUsed 204.33MB rss 572.20MB
2025-9-15 15:23:34-debug:   Number of all scripts: 234
2025-9-15 15:23:34-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-15 15:23:34-debug: run build task 查询 Asset Bundle success in 22 ms √3%
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 199.91MB rss 571.24MB
2025-9-15 15:23:34-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 15:23:34-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 15:23:34-debug: init bundle assets
2025-9-15 15:23:34-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 15:23:34-debug: Number of scenes: 1
2025-9-15 15:23:34-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (7ms)
2025-9-15 15:23:34-debug: run build task 查询使用的资源以及资源包配置 success in 7 ms √7%
2025-9-15 15:23:34-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 15:23:34-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 200.90MB rss 571.36MB
2025-9-15 15:23:34-debug: sort script group...
2025-9-15 15:23:34-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 15:23:34-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 15:23:34-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 201.06MB rss 571.34MB
2025-9-15 15:23:34-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 15:23:34-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 15:23:34-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 15:23:34-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 15:23:34-debug: builder.tasks.settings.macro start11%
2025-9-15 15:23:34-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 201.13MB rss 571.27MB
2025-9-15 15:23:34-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 15:23:34-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 201.17MB rss 571.16MB
2025-9-15 15:23:34-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 15:23:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 15:23:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 15:23:34-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 15:23:34-debug: // ---- build task custom joint texture layouts ----
2025-9-15 15:23:34-debug: custom joint texture layouts start12%
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 201.24MB rss 571.19MB
2025-9-15 15:23:34-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 15:23:34-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 201.27MB rss 571.20MB
2025-9-15 15:23:34-debug: // ---- build task custom joint physics ----
2025-9-15 15:23:34-debug: custom joint physics start12%
2025-9-15 15:23:34-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 15:23:34-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 15:23:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 15:23:34-debug: 填充脚本数据到 settings.json start13%
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 201.30MB rss 571.21MB
2025-9-15 15:23:34-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 15:23:34-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 201.33MB rss 571.20MB
2025-9-15 15:23:34-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 15:23:34-debug: 填充场景数据到 settings.json start13%
2025-9-15 15:23:34-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 15:23:34-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 15:23:34-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 201.36MB rss 571.20MB
2025-9-15 15:23:34-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (7ms)
2025-9-15 15:23:34-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 7 ms √14%
2025-9-15 15:23:34-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 15:23:34-debug: Process: heapTotal 230.55MB heapUsed 202.21MB rss 572.08MB
2025-9-15 15:23:34-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 15:23:34-debug: options.md5Cache is false.
2025-9-15 15:23:34-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 15:23:34-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 15:26:31-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-15 15:26:31-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:26:31-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 15:29:39-debug: refresh db internal success
2025-9-15 15:29:39-debug: asset-db:worker-effect-data-processing (166ms)
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_0.png
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_1.png
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_3.png
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_2.png
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_4.png
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_3.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_3.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_0.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_0.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_4.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir\icon_elixir_bg_4.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\icon\elixir
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:29:39-debug: refresh db assets success
2025-9-15 15:29:39-debug: asset-db:refresh-all-database (430ms)
2025-9-15 15:30:25-debug: refresh db internal success
2025-9-15 15:30:25-debug: asset-db:worker-effect-data-processing (195ms)
2025-9-15 15:30:25-debug: refresh db assets success
2025-9-15 15:30:25-debug: asset-db:refresh-all-database (393ms)
2025-9-15 15:31:14-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-15 15:31:14-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:31:14-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 15:32:17-debug: refresh db internal success
2025-9-15 15:32:17-debug: asset-db:worker-effect-data-processing (174ms)
2025-9-15 15:32:18-debug: refresh db assets success
2025-9-15 15:32:18-debug: asset-db:refresh-all-database (374ms)
2025-9-15 15:32:23-debug: refresh db internal success
2025-9-15 15:32:23-debug: asset-db:worker-effect-data-processing (111ms)
2025-9-15 15:32:23-debug: refresh db assets success
2025-9-15 15:32:23-debug: asset-db:refresh-all-database (264ms)
2025-9-15 15:32:47-debug: refresh db internal success
2025-9-15 15:32:47-debug: asset-db:worker-effect-data-processing (150ms)
2025-9-15 15:32:47-debug: refresh db assets success
2025-9-15 15:32:47-debug: asset-db:refresh-all-database (314ms)
2025-9-15 15:32:54-debug: refresh db internal success
2025-9-15 15:32:54-debug: asset-db:worker-effect-data-processing (111ms)
2025-9-15 15:32:54-debug: refresh db assets success
2025-9-15 15:32:54-debug: asset-db:refresh-all-database (273ms)
2025-9-15 15:33:14-debug: refresh db internal success
2025-9-15 15:33:14-debug: asset-db:worker-effect-data-processing (165ms)
2025-9-15 15:33:14-debug: refresh db assets success
2025-9-15 15:33:14-debug: asset-db:refresh-all-database (339ms)
2025-9-15 15:44:22-debug: refresh db internal success
2025-9-15 15:44:23-debug: asset-db:worker-effect-data-processing (144ms)
2025-9-15 15:44:23-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:44:23-debug: refresh db assets success
2025-9-15 15:44:23-debug: asset-db:refresh-all-database (393ms)
2025-9-15 15:44:24-debug: Query all assets info in project
2025-9-15 15:44:24-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 15:44:24-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 15:44:24-debug:   Number of other assets: 2713
2025-9-15 15:44:24-debug:   Number of all scripts: 234
2025-9-15 15:44:24-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 15:44:24-debug: Process: heapTotal 234.21MB heapUsed 209.26MB rss 575.82MB
2025-9-15 15:44:24-debug: 查询 Asset Bundle start0%
2025-9-15 15:44:24-debug:   Number of all scenes: 2
2025-9-15 15:44:24-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-9-15 15:44:24-debug: run build task 查询 Asset Bundle success in 24 ms √3%
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 206.56MB rss 575.78MB
2025-9-15 15:44:24-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 15:44:24-debug: init bundle assets
2025-9-15 15:44:24-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 15:44:24-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 15:44:24-debug: Number of scenes: 1
2025-9-15 15:44:24-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 15:44:24-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 15:44:24-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 15:44:24-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 15:44:24-debug: sort script group...
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 207.58MB rss 575.40MB
2025-9-15 15:44:24-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-15 15:44:24-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 15:44:24-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 207.73MB rss 575.85MB
2025-9-15 15:44:24-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-15 15:44:24-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 15:44:24-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 15:44:24-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 15:44:24-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 15:44:24-debug: builder.tasks.settings.macro start11%
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 207.82MB rss 575.59MB
2025-9-15 15:44:24-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 15:44:24-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 15:44:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 207.86MB rss 575.40MB
2025-9-15 15:44:24-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 15:44:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-15 15:44:24-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 207.93MB rss 575.21MB
2025-9-15 15:44:24-debug: custom joint texture layouts start12%
2025-9-15 15:44:24-debug: // ---- build task custom joint texture layouts ----
2025-9-15 15:44:24-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 15:44:24-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 15:44:24-debug: custom joint physics start12%
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 207.96MB rss 575.21MB
2025-9-15 15:44:24-debug: // ---- build task custom joint physics ----
2025-9-15 15:44:24-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 15:44:24-debug: 填充脚本数据到 settings.json start13%
2025-9-15 15:44:24-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 207.99MB rss 575.21MB
2025-9-15 15:44:24-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 15:44:24-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 15:44:24-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 208.02MB rss 575.21MB
2025-9-15 15:44:24-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 15:44:24-debug: 填充场景数据到 settings.json start13%
2025-9-15 15:44:24-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 15:44:24-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 15:44:24-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 208.05MB rss 575.21MB
2025-9-15 15:44:24-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (8ms)
2025-9-15 15:44:24-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 8 ms √14%
2025-9-15 15:44:24-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 15:44:24-debug: Process: heapTotal 234.46MB heapUsed 208.92MB rss 576.08MB
2025-9-15 15:44:24-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 15:44:24-debug: options.md5Cache is false.
2025-9-15 15:44:24-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 15:44:24-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 15:45:14-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-15 15:45:14-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:45:14-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 15:45:35-debug: refresh db internal success
2025-9-15 15:45:35-debug: asset-db:worker-effect-data-processing (164ms)
2025-9-15 15:45:35-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:45:35-debug: refresh db assets success
2025-9-15 15:45:35-debug: asset-db:refresh-all-database (384ms)
2025-9-15 15:45:36-debug: Query all assets info in project
2025-9-15 15:45:36-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 15:45:36-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 15:45:36-debug:   Number of all scenes: 2
2025-9-15 15:45:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 15:45:36-debug: 查询 Asset Bundle start0%
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 208.28MB rss 578.47MB
2025-9-15 15:45:36-debug:   Number of other assets: 2713
2025-9-15 15:45:36-debug:   Number of all scripts: 234
2025-9-15 15:45:36-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-15 15:45:36-debug: run build task 查询 Asset Bundle success in 22 ms √3%
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 210.92MB rss 578.57MB
2025-9-15 15:45:36-debug: init bundle assets
2025-9-15 15:45:36-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 15:45:36-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 15:45:36-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 15:45:36-debug: Number of scenes: 1
2025-9-15 15:45:36-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 15:45:36-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 15:45:36-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 15:45:36-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 15:45:36-debug: sort script group...
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 211.93MB rss 578.81MB
2025-9-15 15:45:36-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-15 15:45:36-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 15:45:36-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-15 15:45:36-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 212.08MB rss 578.79MB
2025-9-15 15:45:36-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 15:45:36-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 15:45:36-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 15:45:36-debug: builder.tasks.settings.macro start11%
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 212.16MB rss 578.71MB
2025-9-15 15:45:36-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 15:45:36-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-15 15:45:36-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 212.19MB rss 578.61MB
2025-9-15 15:45:36-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 15:45:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 15:45:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 15:45:36-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 15:45:36-debug: custom joint texture layouts start12%
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 212.26MB rss 578.63MB
2025-9-15 15:45:36-debug: // ---- build task custom joint texture layouts ----
2025-9-15 15:45:36-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 15:45:36-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 15:45:36-debug: custom joint physics start12%
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 212.30MB rss 578.64MB
2025-9-15 15:45:36-debug: // ---- build task custom joint physics ----
2025-9-15 15:45:36-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 15:45:36-debug: 填充脚本数据到 settings.json start13%
2025-9-15 15:45:36-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 212.33MB rss 578.64MB
2025-9-15 15:45:36-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 15:45:36-debug: 填充场景数据到 settings.json start13%
2025-9-15 15:45:36-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 212.35MB rss 578.64MB
2025-9-15 15:45:36-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 15:45:36-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 15:45:36-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 212.38MB rss 578.64MB
2025-9-15 15:45:36-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (8ms)
2025-9-15 15:45:36-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 15:45:36-debug: Process: heapTotal 235.50MB heapUsed 213.21MB rss 579.49MB
2025-9-15 15:45:36-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 15:45:36-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 8 ms √14%
2025-9-15 15:45:36-debug: options.md5Cache is false.
2025-9-15 15:45:36-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 15:46:26-debug: refresh db internal success
2025-9-15 15:46:26-debug: asset-db:worker-effect-data-processing (143ms)
2025-9-15 15:46:26-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:46:26-debug: refresh db assets success
2025-9-15 15:46:26-debug: asset-db:refresh-all-database (318ms)
2025-9-15 15:46:27-debug: Query all assets info in project
2025-9-15 15:46:27-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 15:46:27-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 15:46:27-debug:   Number of all scripts: 234
2025-9-15 15:46:27-debug:   Number of other assets: 2713
2025-9-15 15:46:27-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 15:46:27-debug:   Number of all scenes: 2
2025-9-15 15:46:27-debug: 查询 Asset Bundle start0%
2025-9-15 15:46:27-debug: Process: heapTotal 225.43MB heapUsed 201.81MB rss 567.28MB
2025-9-15 15:46:27-debug: run build task 查询 Asset Bundle success in 16 ms √3%
2025-9-15 15:46:27-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 15:46:27-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 199.43MB rss 568.49MB
2025-9-15 15:46:27-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-15 15:46:27-debug: init bundle assets
2025-9-15 15:46:27-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 15:46:27-debug: Number of scenes: 1
2025-9-15 15:46:27-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (4ms)
2025-9-15 15:46:27-debug: run build task 查询使用的资源以及资源包配置 success in 4 ms √7%
2025-9-15 15:46:27-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 200.43MB rss 568.69MB
2025-9-15 15:46:27-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 15:46:27-debug: sort script group...
2025-9-15 15:46:27-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 15:46:27-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 15:46:27-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 15:46:27-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 200.58MB rss 568.64MB
2025-9-15 15:46:27-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 15:46:27-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 15:46:27-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 15:46:27-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 200.66MB rss 568.63MB
2025-9-15 15:46:27-debug: builder.tasks.settings.macro start11%
2025-9-15 15:46:27-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 15:46:27-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 15:46:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 15:46:27-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 200.70MB rss 568.52MB
2025-9-15 15:46:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 15:46:27-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 200.77MB rss 568.45MB
2025-9-15 15:46:27-debug: custom joint texture layouts start12%
2025-9-15 15:46:27-debug: // ---- build task custom joint texture layouts ----
2025-9-15 15:46:27-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 15:46:27-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 15:46:27-debug: // ---- build task custom joint physics ----
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 200.80MB rss 568.45MB
2025-9-15 15:46:27-debug: custom joint physics start12%
2025-9-15 15:46:27-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 15:46:27-debug: 填充脚本数据到 settings.json start13%
2025-9-15 15:46:27-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 200.83MB rss 568.45MB
2025-9-15 15:46:27-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 15:46:27-debug: 填充场景数据到 settings.json start13%
2025-9-15 15:46:27-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 200.85MB rss 568.45MB
2025-9-15 15:46:27-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 15:46:27-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 15:46:27-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 200.88MB rss 568.45MB
2025-9-15 15:46:27-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (5ms)
2025-9-15 15:46:27-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 5 ms √14%
2025-9-15 15:46:27-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 15:46:27-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 15:46:27-debug: Process: heapTotal 229.43MB heapUsed 201.74MB rss 569.30MB
2025-9-15 15:46:27-debug: options.md5Cache is false.
2025-9-15 15:46:27-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 15:49:59-debug: refresh db internal success
2025-9-15 15:49:59-debug: asset-db:worker-effect-data-processing (109ms)
2025-9-15 15:49:59-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:49:59-debug: refresh db assets success
2025-9-15 15:49:59-debug: asset-db:refresh-all-database (285ms)
2025-9-15 15:50:00-debug: Query all assets info in project
2025-9-15 15:50:00-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 15:50:00-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 15:50:00-debug:   Number of all scripts: 234
2025-9-15 15:50:00-debug:   Number of other assets: 2713
2025-9-15 15:50:00-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 15:50:00-debug: 查询 Asset Bundle start0%
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 212.40MB rss 577.23MB
2025-9-15 15:50:00-debug:   Number of all scenes: 2
2025-9-15 15:50:00-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-15 15:50:00-debug: run build task 查询 Asset Bundle success in 16 ms √3%
2025-9-15 15:50:00-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 15:50:00-debug: init bundle assets
2025-9-15 15:50:00-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 209.77MB rss 576.73MB
2025-9-15 15:50:00-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 15:50:00-debug: Number of scenes: 1
2025-9-15 15:50:00-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (4ms)
2025-9-15 15:50:00-debug: run build task 查询使用的资源以及资源包配置 success in 4 ms √7%
2025-9-15 15:50:00-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 15:50:00-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 210.75MB rss 576.68MB
2025-9-15 15:50:00-debug: sort script group...
2025-9-15 15:50:00-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 15:50:00-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 210.90MB rss 576.64MB
2025-9-15 15:50:00-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 15:50:00-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 15:50:00-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 15:50:00-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 15:50:00-debug: builder.tasks.settings.macro start11%
2025-9-15 15:50:00-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 210.99MB rss 576.46MB
2025-9-15 15:50:00-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 15:50:00-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 15:50:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 15:50:00-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 211.03MB rss 576.46MB
2025-9-15 15:50:00-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 15:50:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 15:50:00-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 15:50:00-debug: custom joint texture layouts start12%
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 211.09MB rss 576.63MB
2025-9-15 15:50:00-debug: // ---- build task custom joint texture layouts ----
2025-9-15 15:50:00-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 15:50:00-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 15:50:00-debug: // ---- build task custom joint physics ----
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 211.13MB rss 576.79MB
2025-9-15 15:50:00-debug: custom joint physics start12%
2025-9-15 15:50:00-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 15:50:00-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 15:50:00-debug: 填充脚本数据到 settings.json start13%
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 211.16MB rss 576.70MB
2025-9-15 15:50:00-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 15:50:00-debug: 填充场景数据到 settings.json start13%
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 211.18MB rss 576.61MB
2025-9-15 15:50:00-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 15:50:00-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 15:50:00-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 15:50:00-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 211.22MB rss 576.57MB
2025-9-15 15:50:00-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 15:50:00-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (3ms)
2025-9-15 15:50:00-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 3 ms √14%
2025-9-15 15:50:00-debug: Process: heapTotal 233.77MB heapUsed 211.90MB rss 576.49MB
2025-9-15 15:50:00-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 15:50:00-debug: options.md5Cache is false.
2025-9-15 15:50:00-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 15:50:00-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 15:50:03-debug: refresh db internal success
2025-9-15 15:50:03-debug: asset-db:worker-effect-data-processing (142ms)
2025-9-15 15:50:03-debug: refresh db assets success
2025-9-15 15:50:03-debug: asset-db:refresh-all-database (298ms)
2025-9-15 15:52:58-debug: refresh db internal success
2025-9-15 15:52:58-debug: asset-db:worker-effect-data-processing (113ms)
2025-9-15 15:52:58-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:52:58-debug: refresh db assets success
2025-9-15 15:52:58-debug: asset-db:refresh-all-database (310ms)
2025-9-15 15:52:59-debug: Query all assets info in project
2025-9-15 15:52:59-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 15:52:59-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 15:52:59-debug:   Number of other assets: 2713
2025-9-15 15:52:59-debug: 查询 Asset Bundle start0%
2025-9-15 15:52:59-debug:   Number of all scripts: 234
2025-9-15 15:52:59-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 198.53MB rss 566.69MB
2025-9-15 15:52:59-debug:   Number of all scenes: 2
2025-9-15 15:52:59-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-9-15 15:52:59-debug: run build task 查询 Asset Bundle success in 18 ms √3%
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 201.26MB rss 567.23MB
2025-9-15 15:52:59-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 15:52:59-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 15:52:59-debug: init bundle assets
2025-9-15 15:52:59-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 15:52:59-debug: Number of scenes: 1
2025-9-15 15:52:59-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 15:52:59-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 15:52:59-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 15:52:59-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 202.29MB rss 566.71MB
2025-9-15 15:52:59-debug: sort script group...
2025-9-15 15:52:59-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 15:52:59-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 15:52:59-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 15:52:59-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 202.44MB rss 566.69MB
2025-9-15 15:52:59-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 15:52:59-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 15:52:59-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 15:52:59-debug: builder.tasks.settings.macro start11%
2025-9-15 15:52:59-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 202.52MB rss 566.60MB
2025-9-15 15:52:59-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 15:52:59-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 15:52:59-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 202.55MB rss 566.48MB
2025-9-15 15:52:59-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 15:52:59-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 15:52:59-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 15:52:59-debug: custom joint texture layouts start12%
2025-9-15 15:52:59-debug: // ---- build task custom joint texture layouts ----
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 202.64MB rss 566.50MB
2025-9-15 15:52:59-debug: // ---- build task custom joint texture layouts ---- (3ms)
2025-9-15 15:52:59-debug: run build task custom joint texture layouts success in 3 ms √12%
2025-9-15 15:52:59-debug: custom joint physics start12%
2025-9-15 15:52:59-debug: // ---- build task custom joint physics ---- (3ms)
2025-9-15 15:52:59-debug: run build task custom joint physics success in 3 ms √13%
2025-9-15 15:52:59-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 15:52:59-debug: 填充脚本数据到 settings.json start13%
2025-9-15 15:52:59-debug: // ---- build task custom joint physics ----
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 202.67MB rss 566.51MB
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 202.70MB rss 566.51MB
2025-9-15 15:52:59-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 15:52:59-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 15:52:59-debug: 填充场景数据到 settings.json start13%
2025-9-15 15:52:59-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 202.73MB rss 566.51MB
2025-9-15 15:52:59-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 15:52:59-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 202.75MB rss 566.51MB
2025-9-15 15:52:59-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 15:52:59-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (7ms)
2025-9-15 15:52:59-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 15:52:59-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 15:52:59-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 7 ms √14%
2025-9-15 15:52:59-debug: Process: heapTotal 226.80MB heapUsed 203.59MB rss 567.43MB
2025-9-15 15:52:59-debug: options.md5Cache is false.
2025-9-15 15:52:59-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 15:53:15-debug: refresh db internal success
2025-9-15 15:53:15-debug: asset-db:worker-effect-data-processing (115ms)
2025-9-15 15:53:15-debug: refresh db assets success
2025-9-15 15:53:15-debug: asset-db:refresh-all-database (279ms)
2025-9-15 15:53:20-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-15 15:53:20-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 15:53:20-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 15:53:24-debug: refresh db internal success
2025-9-15 15:53:24-debug: asset-db:worker-effect-data-processing (131ms)
2025-9-15 15:53:24-debug: refresh db assets success
2025-9-15 15:53:24-debug: asset-db:refresh-all-database (287ms)
2025-9-15 15:53:26-debug: Query all assets info in project
2025-9-15 15:53:26-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 15:53:26-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 15:53:26-debug:   Number of all scenes: 2
2025-9-15 15:53:26-debug:   Number of other assets: 2713
2025-9-15 15:53:26-debug: 查询 Asset Bundle start0%
2025-9-15 15:53:26-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 199.26MB rss 569.55MB
2025-9-15 15:53:26-debug:   Number of all scripts: 234
2025-9-15 15:53:26-debug: // ---- build task 查询 Asset Bundle ---- (14ms)
2025-9-15 15:53:26-debug: run build task 查询 Asset Bundle success in 14 ms √3%
2025-9-15 15:53:26-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 15:53:26-debug: init bundle assets
2025-9-15 15:53:26-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 201.96MB rss 570.29MB
2025-9-15 15:53:26-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 15:53:26-debug: Number of scenes: 1
2025-9-15 15:53:26-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 15:53:26-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 15:53:26-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 15:53:26-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 202.96MB rss 570.25MB
2025-9-15 15:53:26-debug: sort script group...
2025-9-15 15:53:26-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 15:53:26-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 15:53:26-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 15:53:26-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 203.11MB rss 570.22MB
2025-9-15 15:53:26-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 15:53:26-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 15:53:26-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 203.19MB rss 570.14MB
2025-9-15 15:53:26-debug: builder.tasks.settings.macro start11%
2025-9-15 15:53:26-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 15:53:26-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 15:53:26-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 15:53:26-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 203.22MB rss 570.04MB
2025-9-15 15:53:26-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 15:53:26-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 15:53:26-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 203.30MB rss 570.07MB
2025-9-15 15:53:26-debug: custom joint texture layouts start12%
2025-9-15 15:53:26-debug: // ---- build task custom joint texture layouts ----
2025-9-15 15:53:26-debug: run build task custom joint texture layouts success in 0 ms √12%
2025-9-15 15:53:26-debug: // ---- build task custom joint physics ----
2025-9-15 15:53:26-debug: custom joint physics start12%
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 203.33MB rss 570.08MB
2025-9-15 15:53:26-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 15:53:26-debug: 填充脚本数据到 settings.json start13%
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 203.35MB rss 570.08MB
2025-9-15 15:53:26-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 15:53:26-debug: 填充场景数据到 settings.json start13%
2025-9-15 15:53:26-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 15:53:26-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 203.38MB rss 570.08MB
2025-9-15 15:53:26-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 15:53:26-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 15:53:26-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 15:53:26-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 203.41MB rss 570.07MB
2025-9-15 15:53:26-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (9ms)
2025-9-15 15:53:26-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 15:53:26-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 9 ms √14%
2025-9-15 15:53:26-debug: options.md5Cache is false.
2025-9-15 15:53:26-debug: Process: heapTotal 227.40MB heapUsed 204.23MB rss 570.86MB
2025-9-15 15:53:26-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 15:53:26-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 15:53:26-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 16:07:16-debug: refresh db internal success
2025-9-15 16:07:16-debug: asset-db:worker-effect-data-processing (148ms)
2025-9-15 16:07:16-debug: refresh db assets success
2025-9-15 16:07:16-debug: asset-db:refresh-all-database (325ms)
2025-9-15 16:07:18-debug: refresh db internal success
2025-9-15 16:07:18-debug: asset-db:worker-effect-data-processing (103ms)
2025-9-15 16:07:18-debug: refresh db assets success
2025-9-15 16:07:18-debug: asset-db:refresh-all-database (254ms)
2025-9-15 16:08:53-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-15 16:08:53-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 16:08:53-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 16:12:23-debug: refresh db internal success
2025-9-15 16:12:23-debug: asset-db:worker-effect-data-processing (167ms)
2025-9-15 16:12:23-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 16:12:23-debug: refresh db assets success
2025-9-15 16:12:23-debug: asset-db:refresh-all-database (360ms)
2025-9-15 16:12:24-debug: Query all assets info in project
2025-9-15 16:12:24-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 16:12:24-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 16:12:24-debug:   Number of all scripts: 234
2025-9-15 16:12:24-debug:   Number of other assets: 2713
2025-9-15 16:12:24-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 16:12:24-debug: Process: heapTotal 227.45MB heapUsed 202.66MB rss 569.61MB
2025-9-15 16:12:24-debug:   Number of all scenes: 2
2025-9-15 16:12:24-debug: 查询 Asset Bundle start0%
2025-9-15 16:12:24-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-9-15 16:12:24-debug: run build task 查询 Asset Bundle success in 24 ms √3%
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 198.18MB rss 569.35MB
2025-9-15 16:12:24-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 16:12:24-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 16:12:24-debug: init bundle assets
2025-9-15 16:12:24-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 16:12:24-debug: Number of scenes: 1
2025-9-15 16:12:24-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 16:12:24-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 16:12:24-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 16:12:24-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 199.19MB rss 569.37MB
2025-9-15 16:12:24-debug: sort script group...
2025-9-15 16:12:24-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 16:12:24-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 16:12:24-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 199.34MB rss 569.34MB
2025-9-15 16:12:24-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 16:12:24-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 16:12:24-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 16:12:24-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 16:12:24-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 199.42MB rss 569.27MB
2025-9-15 16:12:24-debug: builder.tasks.settings.macro start11%
2025-9-15 16:12:24-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 16:12:24-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 199.45MB rss 569.16MB
2025-9-15 16:12:24-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 16:12:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 16:12:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 16:12:24-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 199.53MB rss 569.20MB
2025-9-15 16:12:24-debug: // ---- build task custom joint texture layouts ----
2025-9-15 16:12:24-debug: custom joint texture layouts start12%
2025-9-15 16:12:24-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 16:12:24-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 199.56MB rss 569.20MB
2025-9-15 16:12:24-debug: // ---- build task custom joint physics ----
2025-9-15 16:12:24-debug: custom joint physics start12%
2025-9-15 16:12:24-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 16:12:24-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 16:12:24-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 16:12:24-debug: 填充脚本数据到 settings.json start13%
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 199.59MB rss 569.20MB
2025-9-15 16:12:24-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 16:12:24-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 199.62MB rss 569.20MB
2025-9-15 16:12:24-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 16:12:24-debug: 填充场景数据到 settings.json start13%
2025-9-15 16:12:24-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 16:12:24-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 16:12:24-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 199.65MB rss 569.19MB
2025-9-15 16:12:24-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 16:12:24-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (8ms)
2025-9-15 16:12:24-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 8 ms √14%
2025-9-15 16:12:24-debug: Process: heapTotal 227.70MB heapUsed 200.49MB rss 570.22MB
2025-9-15 16:12:24-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 16:12:24-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 16:12:24-debug: options.md5Cache is false.
2025-9-15 16:12:24-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 16:12:24-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 16:15:38-debug: refresh db internal success
2025-9-15 16:15:38-debug: asset-db:worker-effect-data-processing (109ms)
2025-9-15 16:15:38-debug: refresh db assets success
2025-9-15 16:15:38-debug: asset-db:refresh-all-database (274ms)
2025-9-15 16:18:26-debug: refresh db internal success
2025-9-15 16:18:26-debug: asset-db:worker-effect-data-processing (175ms)
2025-9-15 16:18:27-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 16:18:27-debug: refresh db assets success
2025-9-15 16:18:27-debug: asset-db:refresh-all-database (602ms)
2025-9-15 16:18:28-debug: Query all assets info in project
2025-9-15 16:18:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 16:18:28-debug:   Number of all scenes: 2
2025-9-15 16:18:28-debug:   Number of all scripts: 234
2025-9-15 16:18:28-debug:   Number of other assets: 2713
2025-9-15 16:18:28-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 16:18:28-debug: Process: heapTotal 225.61MB heapUsed 201.09MB rss 566.34MB
2025-9-15 16:18:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 16:18:28-debug: 查询 Asset Bundle start0%
2025-9-15 16:18:28-debug: // ---- build task 查询 Asset Bundle ---- (41ms)
2025-9-15 16:18:28-debug: run build task 查询 Asset Bundle success in 41 ms √3%
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 197.01MB rss 566.88MB
2025-9-15 16:18:28-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 16:18:28-debug: init bundle assets
2025-9-15 16:18:28-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 16:18:28-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 16:18:28-debug: Number of scenes: 1
2025-9-15 16:18:28-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (15ms)
2025-9-15 16:18:28-debug: run build task 查询使用的资源以及资源包配置 success in 15 ms √7%
2025-9-15 16:18:28-debug: sort script group...
2025-9-15 16:18:28-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 198.01MB rss 566.91MB
2025-9-15 16:18:28-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 16:18:28-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-15 16:18:28-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-15 16:18:28-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 16:18:28-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 198.16MB rss 566.89MB
2025-9-15 16:18:28-debug: // ---- build task 初始化 settings.json 与 config.json ---- (3ms)
2025-9-15 16:18:28-debug: run build task 初始化 settings.json 与 config.json success in 3 ms √11%
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 198.24MB rss 566.81MB
2025-9-15 16:18:28-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 16:18:28-debug: builder.tasks.settings.macro start11%
2025-9-15 16:18:28-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 16:18:28-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-15 16:18:28-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 16:18:28-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-15 16:18:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 198.28MB rss 566.70MB
2025-9-15 16:18:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-9-15 16:18:28-debug: run build task 整理部分构建选项内数据到 settings.json success in 4 ms √12%
2025-9-15 16:18:28-debug: custom joint texture layouts start12%
2025-9-15 16:18:28-debug: // ---- build task custom joint texture layouts ----
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 198.35MB rss 566.75MB
2025-9-15 16:18:28-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 16:18:28-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 16:18:28-debug: custom joint physics start12%
2025-9-15 16:18:28-debug: // ---- build task custom joint physics ----
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 198.39MB rss 566.75MB
2025-9-15 16:18:28-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 198.42MB rss 566.75MB
2025-9-15 16:18:28-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 16:18:28-debug: 填充脚本数据到 settings.json start13%
2025-9-15 16:18:28-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 16:18:28-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 16:18:28-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 198.45MB rss 566.75MB
2025-9-15 16:18:28-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 16:18:28-debug: 填充场景数据到 settings.json start13%
2025-9-15 16:18:28-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 16:18:28-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 16:18:28-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 16:18:28-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 198.48MB rss 566.75MB
2025-9-15 16:18:28-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 16:18:28-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 16:18:28-debug: Process: heapTotal 225.86MB heapUsed 199.32MB rss 567.71MB
2025-9-15 16:18:28-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (10ms)
2025-9-15 16:18:28-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 10 ms √14%
2025-9-15 16:18:28-debug: options.md5Cache is false.
2025-9-15 16:18:28-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (2ms)
2025-9-15 16:18:28-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 2 ms √15%
2025-9-15 16:18:30-debug: refresh db internal success
2025-9-15 16:18:30-debug: asset-db:worker-effect-data-processing (199ms)
2025-9-15 16:18:30-debug: refresh db assets success
2025-9-15 16:18:30-debug: asset-db:refresh-all-database (569ms)
2025-9-15 16:20:37-debug: refresh db internal success
2025-9-15 16:20:37-debug: asset-db:worker-effect-data-processing (143ms)
2025-9-15 16:20:38-debug: refresh db assets success
2025-9-15 16:20:38-debug: asset-db:refresh-all-database (312ms)
2025-9-15 16:20:52-debug: refresh db internal success
2025-9-15 16:20:52-debug: asset-db:worker-effect-data-processing (116ms)
2025-9-15 16:20:52-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 16:20:52-debug: refresh db assets success
2025-9-15 16:20:52-debug: asset-db:refresh-all-database (291ms)
2025-9-15 16:20:53-debug: Query all assets info in project
2025-9-15 16:20:53-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 16:20:53-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 16:20:53-debug:   Number of all scenes: 2
2025-9-15 16:20:53-debug:   Number of other assets: 2713
2025-9-15 16:20:53-debug:   Number of all scripts: 234
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 208.05MB rss 575.98MB
2025-9-15 16:20:53-debug: 查询 Asset Bundle start0%
2025-9-15 16:20:53-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 16:20:53-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-15 16:20:53-debug: run build task 查询 Asset Bundle success in 20 ms √3%
2025-9-15 16:20:53-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 16:20:53-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 204.50MB rss 576.09MB
2025-9-15 16:20:53-debug: init bundle assets
2025-9-15 16:20:53-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 16:20:53-debug: Number of scenes: 1
2025-9-15 16:20:53-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 16:20:53-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 16:20:53-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 205.52MB rss 575.59MB
2025-9-15 16:20:53-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 16:20:53-debug: sort script group...
2025-9-15 16:20:53-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 16:20:53-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 16:20:53-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 205.67MB rss 575.57MB
2025-9-15 16:20:53-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 16:20:53-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 16:20:53-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 16:20:53-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 16:20:53-debug: builder.tasks.settings.macro start11%
2025-9-15 16:20:53-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 205.75MB rss 575.48MB
2025-9-15 16:20:53-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 16:20:53-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 205.79MB rss 575.36MB
2025-9-15 16:20:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 16:20:53-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 16:20:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 16:20:53-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 16:20:53-debug: // ---- build task custom joint texture layouts ----
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 205.86MB rss 575.39MB
2025-9-15 16:20:53-debug: custom joint texture layouts start12%
2025-9-15 16:20:53-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 16:20:53-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 16:20:53-debug: custom joint physics start12%
2025-9-15 16:20:53-debug: // ---- build task custom joint physics ----
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 205.89MB rss 575.39MB
2025-9-15 16:20:53-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 16:20:53-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 16:20:53-debug: 填充脚本数据到 settings.json start13%
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 205.92MB rss 575.39MB
2025-9-15 16:20:53-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 16:20:53-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 16:20:53-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 205.95MB rss 575.39MB
2025-9-15 16:20:53-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 16:20:53-debug: 填充场景数据到 settings.json start13%
2025-9-15 16:20:53-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 16:20:53-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 205.98MB rss 575.39MB
2025-9-15 16:20:53-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 16:20:53-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 5 ms √14%
2025-9-15 16:20:53-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 16:20:53-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (5ms)
2025-9-15 16:20:53-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 16:20:53-debug: Process: heapTotal 232.75MB heapUsed 206.85MB rss 576.20MB
2025-9-15 16:20:53-debug: options.md5Cache is false.
2025-9-15 16:20:53-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 16:21:35-debug: Query all assets info in project
2025-9-15 16:21:35-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 16:21:35-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 16:21:35-debug:   Number of all scripts: 234
2025-9-15 16:21:35-debug:   Number of all scenes: 2
2025-9-15 16:21:35-debug: 查询 Asset Bundle start0%
2025-9-15 16:21:35-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 16:21:35-debug:   Number of other assets: 2713
2025-9-15 16:21:35-debug: Process: heapTotal 217.75MB heapUsed 207.11MB rss 559.57MB
2025-9-15 16:21:35-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-9-15 16:21:35-debug: run build task 查询 Asset Bundle success in 18 ms √3%
2025-9-15 16:21:35-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 16:21:35-debug: Process: heapTotal 220.75MB heapUsed 208.31MB rss 562.88MB
2025-9-15 16:21:35-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 16:21:35-debug: init bundle assets
2025-9-15 16:21:35-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 16:21:35-debug: Number of scenes: 1
2025-9-15 16:21:35-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (4ms)
2025-9-15 16:21:35-debug: run build task 查询使用的资源以及资源包配置 success in 4 ms √7%
2025-9-15 16:21:35-debug: Process: heapTotal 220.75MB heapUsed 209.31MB rss 562.87MB
2025-9-15 16:21:35-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 16:21:35-debug: sort script group...
2025-9-15 16:21:35-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 16:21:35-debug: run build task 整理脚本分组与脚本数据 success in 0 ms √10%
2025-9-15 16:21:35-debug: Process: heapTotal 220.75MB heapUsed 209.45MB rss 562.87MB
2025-9-15 16:21:35-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 16:21:35-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 16:21:35-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 16:21:35-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 16:21:35-debug: builder.tasks.settings.macro start11%
2025-9-15 16:21:35-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 16:21:35-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 16:21:35-debug: Process: heapTotal 220.75MB heapUsed 209.53MB rss 562.80MB
2025-9-15 16:21:35-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 16:21:35-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 16:21:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 16:21:35-debug: Process: heapTotal 220.75MB heapUsed 209.56MB rss 562.79MB
2025-9-15 16:21:35-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 16:21:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 16:21:35-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 16:21:35-debug: Process: heapTotal 220.75MB heapUsed 209.63MB rss 562.79MB
2025-9-15 16:21:35-debug: custom joint texture layouts start12%
2025-9-15 16:21:35-debug: // ---- build task custom joint texture layouts ----
2025-9-15 16:21:35-debug: run build task custom joint texture layouts success in 0 ms √12%
2025-9-15 16:21:35-debug: Process: heapTotal 220.75MB heapUsed 209.66MB rss 562.79MB
2025-9-15 16:21:35-debug: // ---- build task custom joint physics ----
2025-9-15 16:21:35-debug: custom joint physics start12%
2025-9-15 16:21:35-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 16:21:35-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 16:21:35-debug: 填充脚本数据到 settings.json start13%
2025-9-15 16:21:35-debug: Process: heapTotal 220.75MB heapUsed 209.68MB rss 562.79MB
2025-9-15 16:21:35-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 16:21:35-debug: 填充场景数据到 settings.json start13%
2025-9-15 16:21:35-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 16:21:35-debug: Process: heapTotal 220.75MB heapUsed 209.71MB rss 562.79MB
2025-9-15 16:21:35-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 16:21:35-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 16:21:35-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 16:21:35-debug: Process: heapTotal 220.75MB heapUsed 209.75MB rss 562.79MB
2025-9-15 16:21:35-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (5ms)
2025-9-15 16:21:35-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 5 ms √14%
2025-9-15 16:21:35-debug: Process: heapTotal 224.75MB heapUsed 209.19MB rss 562.70MB
2025-9-15 16:21:35-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 16:21:35-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 16:21:35-debug: options.md5Cache is false.
2025-9-15 16:21:35-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 16:22:05-debug: refresh db internal success
2025-9-15 16:22:06-debug: asset-db:worker-effect-data-processing (148ms)
2025-9-15 16:22:06-debug: refresh db assets success
2025-9-15 16:22:06-debug: asset-db:refresh-all-database (311ms)
2025-9-15 16:22:15-debug: refresh db internal success
2025-9-15 16:22:15-debug: asset-db:worker-effect-data-processing (107ms)
2025-9-15 16:22:16-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 16:22:16-debug: refresh db assets success
2025-9-15 16:22:16-debug: asset-db:refresh-all-database (279ms)
2025-9-15 16:22:16-debug: Query all assets info in project
2025-9-15 16:22:16-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 16:22:16-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 16:22:16-debug:   Number of all scripts: 234
2025-9-15 16:22:16-debug:   Number of all scenes: 2
2025-9-15 16:22:16-debug:   Number of other assets: 2713
2025-9-15 16:22:16-debug: Process: heapTotal 225.86MB heapUsed 198.66MB rss 564.96MB
2025-9-15 16:22:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 16:22:16-debug: 查询 Asset Bundle start0%
2025-9-15 16:22:16-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-15 16:22:16-debug: run build task 查询 Asset Bundle success in 19 ms √3%
2025-9-15 16:22:16-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 16:22:16-debug: init bundle assets
2025-9-15 16:22:16-debug: Process: heapTotal 225.86MB heapUsed 195.11MB rss 564.00MB
2025-9-15 16:22:16-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 16:22:16-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 16:22:16-debug: Number of scenes: 1
2025-9-15 16:22:17-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (5ms)
2025-9-15 16:22:17-debug: run build task 查询使用的资源以及资源包配置 success in 5 ms √7%
2025-9-15 16:22:17-debug: Process: heapTotal 225.86MB heapUsed 196.13MB rss 563.97MB
2025-9-15 16:22:17-debug: sort script group...
2025-9-15 16:22:17-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 16:22:17-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 16:22:17-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 16:22:17-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 16:22:17-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 16:22:17-debug: Process: heapTotal 225.86MB heapUsed 196.28MB rss 563.95MB
2025-9-15 16:22:17-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 16:22:17-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 16:22:17-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 16:22:17-debug: builder.tasks.settings.macro start11%
2025-9-15 16:22:17-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 16:22:17-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 16:22:17-debug: Process: heapTotal 225.86MB heapUsed 196.36MB rss 563.87MB
2025-9-15 16:22:17-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-15 16:22:17-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-15 16:22:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 16:22:17-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 16:22:17-debug: Process: heapTotal 225.86MB heapUsed 196.39MB rss 563.77MB
2025-9-15 16:22:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-9-15 16:22:17-debug: run build task 整理部分构建选项内数据到 settings.json success in 4 ms √12%
2025-9-15 16:22:17-debug: custom joint texture layouts start12%
2025-9-15 16:22:17-debug: Process: heapTotal 225.86MB heapUsed 196.47MB rss 563.82MB
2025-9-15 16:22:17-debug: // ---- build task custom joint texture layouts ----
2025-9-15 16:22:17-debug: run build task custom joint texture layouts success in 0 ms √12%
2025-9-15 16:22:17-debug: custom joint physics start12%
2025-9-15 16:22:17-debug: // ---- build task custom joint physics ----
2025-9-15 16:22:17-debug: Process: heapTotal 225.86MB heapUsed 196.49MB rss 563.82MB
2025-9-15 16:22:17-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 16:22:17-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 16:22:17-debug: 填充脚本数据到 settings.json start13%
2025-9-15 16:22:17-debug: Process: heapTotal 225.86MB heapUsed 196.52MB rss 563.82MB
2025-9-15 16:22:17-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 16:22:17-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 16:22:17-debug: 填充场景数据到 settings.json start13%
2025-9-15 16:22:17-debug: Process: heapTotal 225.86MB heapUsed 196.54MB rss 563.82MB
2025-9-15 16:22:17-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 16:22:17-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 16:22:17-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 16:22:17-debug: Process: heapTotal 225.86MB heapUsed 196.57MB rss 563.81MB
2025-9-15 16:22:17-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-15 16:22:17-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-15 16:22:17-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 16:22:17-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 16:22:17-debug: options.md5Cache is false.
2025-9-15 16:22:17-debug: Process: heapTotal 225.86MB heapUsed 197.43MB rss 564.71MB
2025-9-15 16:22:17-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 16:23:43-debug: refresh db internal success
2025-9-15 16:23:44-debug: asset-db:worker-effect-data-processing (273ms)
2025-9-15 16:23:44-debug: refresh db assets success
2025-9-15 16:23:44-debug: asset-db:refresh-all-database (619ms)
2025-9-15 16:24:22-debug: refresh db internal success
2025-9-15 16:24:22-debug: asset-db:worker-effect-data-processing (136ms)
2025-9-15 16:24:23-debug: refresh db assets success
2025-9-15 16:24:23-debug: asset-db:refresh-all-database (298ms)
2025-9-15 16:24:36-debug: refresh db internal success
2025-9-15 16:24:36-debug: asset-db:worker-effect-data-processing (115ms)
2025-9-15 16:24:36-debug: refresh db assets success
2025-9-15 16:24:36-debug: asset-db:refresh-all-database (281ms)
2025-9-15 16:26:00-debug: refresh db internal success
2025-9-15 16:26:00-debug: asset-db:worker-effect-data-processing (142ms)
2025-9-15 16:26:00-debug: refresh db assets success
2025-9-15 16:26:00-debug: asset-db:refresh-all-database (301ms)
2025-9-15 16:30:23-debug: refresh db internal success
2025-9-15 16:30:23-debug: asset-db:worker-effect-data-processing (145ms)
2025-9-15 16:30:23-debug: refresh db assets success
2025-9-15 16:30:23-debug: asset-db:refresh-all-database (306ms)
2025-9-15 16:36:03-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-15 16:36:03-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 16:36:03-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 16:36:17-debug: refresh db internal success
2025-9-15 16:36:17-debug: asset-db:worker-effect-data-processing (199ms)
2025-9-15 16:36:17-debug: refresh db assets success
2025-9-15 16:36:17-debug: asset-db:refresh-all-database (390ms)
2025-9-15 16:36:22-debug: refresh db internal success
2025-9-15 16:36:22-debug: asset-db:worker-effect-data-processing (126ms)
2025-9-15 16:36:22-debug: refresh db assets success
2025-9-15 16:36:22-debug: asset-db:refresh-all-database (313ms)
2025-9-15 16:36:30-debug: refresh db internal success
2025-9-15 16:36:30-debug: asset-db:worker-effect-data-processing (145ms)
2025-9-15 16:36:30-debug: refresh db assets success
2025-9-15 16:36:30-debug: asset-db:refresh-all-database (324ms)
2025-9-15 16:36:37-debug: refresh db internal success
2025-9-15 16:36:37-debug: asset-db:worker-effect-data-processing (157ms)
2025-9-15 16:36:38-debug: refresh db assets success
2025-9-15 16:36:38-debug: asset-db:refresh-all-database (341ms)
2025-9-15 16:41:29-debug: refresh db internal success
2025-9-15 16:41:29-debug: asset-db:worker-effect-data-processing (138ms)
2025-9-15 16:41:29-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 16:41:29-debug: refresh db assets success
2025-9-15 16:41:29-debug: asset-db:refresh-all-database (376ms)
2025-9-15 16:41:30-debug: Query all assets info in project
2025-9-15 16:41:30-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 16:41:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 16:41:30-debug:   Number of other assets: 2713
2025-9-15 16:41:30-debug:   Number of all scenes: 2
2025-9-15 16:41:30-debug:   Number of all scripts: 234
2025-9-15 16:41:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 203.25MB rss 571.19MB
2025-9-15 16:41:30-debug: 查询 Asset Bundle start0%
2025-9-15 16:41:30-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-15 16:41:30-debug: run build task 查询 Asset Bundle success in 21 ms √3%
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 200.72MB rss 571.16MB
2025-9-15 16:41:30-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 16:41:30-debug: init bundle assets
2025-9-15 16:41:30-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 16:41:30-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 16:41:30-debug: Number of scenes: 1
2025-9-15 16:41:30-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (5ms)
2025-9-15 16:41:30-debug: run build task 查询使用的资源以及资源包配置 success in 5 ms √7%
2025-9-15 16:41:30-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 201.75MB rss 570.15MB
2025-9-15 16:41:30-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 16:41:30-debug: sort script group...
2025-9-15 16:41:30-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 16:41:30-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 16:41:30-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 16:41:30-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 201.90MB rss 570.13MB
2025-9-15 16:41:30-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 16:41:30-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 16:41:30-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 16:41:30-debug: builder.tasks.settings.macro start11%
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 201.98MB rss 570.04MB
2025-9-15 16:41:30-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 16:41:30-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-15 16:41:30-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-15 16:41:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 16:41:30-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 202.02MB rss 569.94MB
2025-9-15 16:41:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-15 16:41:30-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-15 16:41:30-debug: // ---- build task custom joint texture layouts ----
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 202.10MB rss 569.98MB
2025-9-15 16:41:30-debug: custom joint texture layouts start12%
2025-9-15 16:41:30-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 16:41:30-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 202.14MB rss 569.99MB
2025-9-15 16:41:30-debug: // ---- build task custom joint physics ----
2025-9-15 16:41:30-debug: custom joint physics start12%
2025-9-15 16:41:30-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 16:41:30-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 16:41:30-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 16:41:30-debug: 填充脚本数据到 settings.json start13%
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 202.17MB rss 569.99MB
2025-9-15 16:41:30-debug: 填充场景数据到 settings.json start13%
2025-9-15 16:41:30-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 202.19MB rss 569.98MB
2025-9-15 16:41:30-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 16:41:30-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 16:41:30-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 16:41:30-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 16:41:30-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 202.22MB rss 569.97MB
2025-9-15 16:41:30-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-15 16:41:30-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 16:41:30-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-15 16:41:30-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 16:41:30-debug: Process: heapTotal 228.49MB heapUsed 203.05MB rss 570.71MB
2025-9-15 16:41:30-debug: options.md5Cache is false.
2025-9-15 16:41:30-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 16:41:30-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 16:49:07-debug: refresh db internal success
2025-9-15 16:49:08-debug: asset-db:worker-effect-data-processing (118ms)
2025-9-15 16:49:08-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 16:49:08-debug: refresh db assets success
2025-9-15 16:49:08-debug: asset-db:refresh-all-database (311ms)
2025-9-15 16:49:09-debug: Query all assets info in project
2025-9-15 16:49:09-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 16:49:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 16:49:09-debug:   Number of other assets: 2713
2025-9-15 16:49:09-debug:   Number of all scenes: 2
2025-9-15 16:49:09-debug:   Number of all scripts: 234
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 198.92MB rss 570.05MB
2025-9-15 16:49:09-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 16:49:09-debug: 查询 Asset Bundle start0%
2025-9-15 16:49:09-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-15 16:49:09-debug: run build task 查询 Asset Bundle success in 16 ms √3%
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 201.57MB rss 570.16MB
2025-9-15 16:49:09-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 16:49:09-debug: init bundle assets
2025-9-15 16:49:09-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 16:49:09-debug: Number of scenes: 1
2025-9-15 16:49:09-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 16:49:09-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 16:49:09-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 16:49:09-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 202.58MB rss 570.14MB
2025-9-15 16:49:09-debug: sort script group...
2025-9-15 16:49:09-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 16:49:09-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 16:49:09-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 16:49:09-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 202.73MB rss 570.13MB
2025-9-15 16:49:09-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 16:49:09-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 16:49:09-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 16:49:09-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 16:49:09-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 16:49:09-debug: builder.tasks.settings.macro start11%
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 202.81MB rss 570.13MB
2025-9-15 16:49:09-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 16:49:09-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 16:49:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 16:49:09-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 202.85MB rss 570.04MB
2025-9-15 16:49:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 16:49:09-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 16:49:09-debug: custom joint texture layouts start12%
2025-9-15 16:49:09-debug: // ---- build task custom joint texture layouts ----
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 202.92MB rss 569.99MB
2025-9-15 16:49:09-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 16:49:09-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 16:49:09-debug: custom joint physics start12%
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 202.95MB rss 569.99MB
2025-9-15 16:49:09-debug: // ---- build task custom joint physics ----
2025-9-15 16:49:09-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 16:49:09-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 202.98MB rss 569.99MB
2025-9-15 16:49:09-debug: 填充脚本数据到 settings.json start13%
2025-9-15 16:49:09-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 203.01MB rss 569.99MB
2025-9-15 16:49:09-debug: 填充场景数据到 settings.json start13%
2025-9-15 16:49:09-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 16:49:09-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 16:49:09-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 16:49:09-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 203.03MB rss 569.99MB
2025-9-15 16:49:09-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-15 16:49:09-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-15 16:49:09-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 16:49:09-debug: options.md5Cache is false.
2025-9-15 16:49:09-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 16:49:09-debug: Process: heapTotal 229.45MB heapUsed 203.90MB rss 570.84MB
2025-9-15 16:49:09-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 16:49:09-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 16:49:31-debug: refresh db internal success
2025-9-15 16:49:31-debug: asset-db:worker-effect-data-processing (113ms)
2025-9-15 16:49:31-debug: refresh db assets success
2025-9-15 16:49:31-debug: asset-db:refresh-all-database (295ms)
2025-9-15 16:50:56-debug: refresh db internal success
2025-9-15 16:50:56-debug: asset-db:worker-effect-data-processing (144ms)
2025-9-15 16:50:56-debug: refresh db assets success
2025-9-15 16:50:56-debug: asset-db:refresh-all-database (301ms)
2025-9-15 16:53:05-debug: refresh db internal success
2025-9-15 16:53:05-debug: asset-db:worker-effect-data-processing (173ms)
2025-9-15 16:53:05-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 16:53:05-debug: refresh db assets success
2025-9-15 16:53:05-debug: asset-db:refresh-all-database (394ms)
2025-9-15 16:53:07-debug: Query all assets info in project
2025-9-15 16:53:07-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 16:53:07-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 16:53:07-debug:   Number of all scenes: 2
2025-9-15 16:53:07-debug:   Number of other assets: 2713
2025-9-15 16:53:07-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 201.99MB rss 571.20MB
2025-9-15 16:53:07-debug: 查询 Asset Bundle start0%
2025-9-15 16:53:07-debug:   Number of all scripts: 234
2025-9-15 16:53:07-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-15 16:53:07-debug: run build task 查询 Asset Bundle success in 22 ms √3%
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 197.97MB rss 571.67MB
2025-9-15 16:53:07-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 16:53:07-debug: init bundle assets
2025-9-15 16:53:07-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 16:53:07-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 16:53:07-debug: Number of scenes: 1
2025-9-15 16:53:07-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 16:53:07-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 16:53:07-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 16:53:07-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 198.99MB rss 571.07MB
2025-9-15 16:53:07-debug: sort script group...
2025-9-15 16:53:07-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-15 16:53:07-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-15 16:53:07-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 16:53:07-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 199.14MB rss 571.04MB
2025-9-15 16:53:07-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 16:53:07-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 16:53:07-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 199.21MB rss 570.95MB
2025-9-15 16:53:07-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 16:53:07-debug: builder.tasks.settings.macro start11%
2025-9-15 16:53:07-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 16:53:07-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 16:53:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 16:53:07-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 199.25MB rss 570.81MB
2025-9-15 16:53:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 16:53:07-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 199.32MB rss 570.84MB
2025-9-15 16:53:07-debug: custom joint texture layouts start12%
2025-9-15 16:53:07-debug: // ---- build task custom joint texture layouts ----
2025-9-15 16:53:07-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 16:53:07-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 199.36MB rss 570.84MB
2025-9-15 16:53:07-debug: custom joint physics start12%
2025-9-15 16:53:07-debug: // ---- build task custom joint physics ----
2025-9-15 16:53:07-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 16:53:07-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 16:53:07-debug: 填充脚本数据到 settings.json start13%
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 199.38MB rss 570.84MB
2025-9-15 16:53:07-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 16:53:07-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 199.41MB rss 570.84MB
2025-9-15 16:53:07-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 16:53:07-debug: 填充场景数据到 settings.json start13%
2025-9-15 16:53:07-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 16:53:07-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 16:53:07-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 199.44MB rss 570.84MB
2025-9-15 16:53:07-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (8ms)
2025-9-15 16:53:07-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 16:53:07-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 8 ms √14%
2025-9-15 16:53:07-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 16:53:07-debug: options.md5Cache is false.
2025-9-15 16:53:07-debug: Process: heapTotal 228.83MB heapUsed 200.31MB rss 572.14MB
2025-9-15 16:53:07-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 16:53:07-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 16:53:15-debug: refresh db internal success
2025-9-15 16:53:15-debug: asset-db:worker-effect-data-processing (108ms)
2025-9-15 16:53:15-debug: refresh db assets success
2025-9-15 16:53:15-debug: asset-db:refresh-all-database (259ms)
2025-9-15 16:58:30-debug: refresh db internal success
2025-9-15 16:58:30-debug: asset-db:worker-effect-data-processing (128ms)
2025-9-15 16:58:30-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 16:58:30-debug: refresh db assets success
2025-9-15 16:58:30-debug: asset-db:refresh-all-database (302ms)
2025-9-15 16:58:31-debug: Query all assets info in project
2025-9-15 16:58:31-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 16:58:31-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 16:58:31-debug:   Number of all scenes: 2
2025-9-15 16:58:31-debug:   Number of all scripts: 234
2025-9-15 16:58:31-debug:   Number of other assets: 2713
2025-9-15 16:58:31-debug: 查询 Asset Bundle start0%
2025-9-15 16:58:31-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 16:58:31-debug: Process: heapTotal 240.34MB heapUsed 214.81MB rss 579.80MB
2025-9-15 16:58:31-debug: // ---- build task 查询 Asset Bundle ---- (51ms)
2025-9-15 16:58:31-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 16:58:31-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 16:58:31-debug: init bundle assets
2025-9-15 16:58:31-debug: run build task 查询 Asset Bundle success in 51 ms √3%
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 195.34MB rss 567.78MB
2025-9-15 16:58:31-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 16:58:31-debug: Number of scenes: 1
2025-9-15 16:58:31-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (7ms)
2025-9-15 16:58:31-debug: run build task 查询使用的资源以及资源包配置 success in 7 ms √7%
2025-9-15 16:58:31-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 196.34MB rss 564.42MB
2025-9-15 16:58:31-debug: sort script group...
2025-9-15 16:58:31-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 16:58:31-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 16:58:31-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 16:58:31-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 16:58:31-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 196.49MB rss 564.40MB
2025-9-15 16:58:31-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 16:58:31-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 16:58:31-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 16:58:31-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 16:58:31-debug: builder.tasks.settings.macro start11%
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 196.57MB rss 564.32MB
2025-9-15 16:58:31-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 16:58:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 196.60MB rss 564.22MB
2025-9-15 16:58:31-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 16:58:31-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 16:58:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-9-15 16:58:31-debug: run build task 整理部分构建选项内数据到 settings.json success in 5 ms √12%
2025-9-15 16:58:31-debug: // ---- build task custom joint texture layouts ----
2025-9-15 16:58:31-debug: custom joint texture layouts start12%
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 196.68MB rss 564.28MB
2025-9-15 16:58:31-debug: run build task custom joint texture layouts success in 0 ms √12%
2025-9-15 16:58:31-debug: custom joint physics start12%
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 196.71MB rss 564.29MB
2025-9-15 16:58:31-debug: // ---- build task custom joint physics ----
2025-9-15 16:58:31-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 16:58:31-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 16:58:31-debug: 填充脚本数据到 settings.json start13%
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 196.74MB rss 564.29MB
2025-9-15 16:58:31-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 16:58:31-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 16:58:31-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 196.77MB rss 564.29MB
2025-9-15 16:58:31-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 16:58:31-debug: 填充场景数据到 settings.json start13%
2025-9-15 16:58:31-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 16:58:31-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 16:58:31-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 16:58:31-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 196.80MB rss 564.54MB
2025-9-15 16:58:31-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (4ms)
2025-9-15 16:58:31-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 4 ms √14%
2025-9-15 16:58:31-debug: Process: heapTotal 228.75MB heapUsed 197.64MB rss 565.07MB
2025-9-15 16:58:31-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 16:58:31-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 16:58:31-debug: options.md5Cache is false.
2025-9-15 16:58:31-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 16:58:31-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 16:58:53-debug: refresh db internal success
2025-9-15 16:58:53-debug: asset-db:worker-effect-data-processing (174ms)
2025-9-15 16:58:53-debug: refresh db assets success
2025-9-15 16:58:53-debug: asset-db:refresh-all-database (528ms)
2025-9-15 16:59:28-debug: Query all assets info in project
2025-9-15 16:59:28-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 16:59:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 16:59:28-debug:   Number of all scenes: 2
2025-9-15 16:59:28-debug:   Number of all scripts: 234
2025-9-15 16:59:28-debug:   Number of other assets: 2713
2025-9-15 16:59:28-debug: Process: heapTotal 219.69MB heapUsed 206.41MB rss 561.72MB
2025-9-15 16:59:28-debug: 查询 Asset Bundle start0%
2025-9-15 16:59:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 16:59:28-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-15 16:59:28-debug: run build task 查询 Asset Bundle success in 21 ms √3%
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 207.44MB rss 562.29MB
2025-9-15 16:59:28-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 16:59:28-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 16:59:28-debug: init bundle assets
2025-9-15 16:59:28-debug: Number of scenes: 1
2025-9-15 16:59:28-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 16:59:28-debug: run build task 查询使用的资源以及资源包配置 success in 4 ms √7%
2025-9-15 16:59:28-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (4ms)
2025-9-15 16:59:28-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 16:59:28-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 16:59:28-debug: sort script group...
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 207.75MB rss 563.21MB
2025-9-15 16:59:28-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 16:59:28-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 16:59:28-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 16:59:28-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 207.91MB rss 563.18MB
2025-9-15 16:59:28-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 16:59:28-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 16:59:28-debug: builder.tasks.settings.macro start11%
2025-9-15 16:59:28-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 207.98MB rss 563.11MB
2025-9-15 16:59:28-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 16:59:28-debug: run build task builder.tasks.settings.macro success in 0 ms √11%
2025-9-15 16:59:28-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 16:59:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 208.01MB rss 562.98MB
2025-9-15 16:59:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-15 16:59:28-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms √12%
2025-9-15 16:59:28-debug: custom joint texture layouts start12%
2025-9-15 16:59:28-debug: // ---- build task custom joint texture layouts ----
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 208.08MB rss 563.00MB
2025-9-15 16:59:28-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 16:59:28-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 208.12MB rss 563.00MB
2025-9-15 16:59:28-debug: // ---- build task custom joint physics ----
2025-9-15 16:59:28-debug: custom joint physics start12%
2025-9-15 16:59:28-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 16:59:28-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 16:59:28-debug: 填充脚本数据到 settings.json start13%
2025-9-15 16:59:28-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 208.15MB rss 563.00MB
2025-9-15 16:59:28-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 16:59:28-debug: 填充场景数据到 settings.json start13%
2025-9-15 16:59:28-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 208.17MB rss 563.00MB
2025-9-15 16:59:28-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 16:59:28-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 16:59:28-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 16:59:28-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 208.20MB rss 563.00MB
2025-9-15 16:59:28-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 3 ms √14%
2025-9-15 16:59:28-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 16:59:28-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (3ms)
2025-9-15 16:59:28-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 16:59:28-debug: Process: heapTotal 220.69MB heapUsed 208.34MB rss 563.02MB
2025-9-15 16:59:28-debug: options.md5Cache is false.
2025-9-15 16:59:28-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 16:59:49-debug: refresh db internal success
2025-9-15 16:59:49-debug: asset-db:worker-effect-data-processing (249ms)
2025-9-15 16:59:49-debug: refresh db assets success
2025-9-15 16:59:49-debug: asset-db:refresh-all-database (601ms)
2025-9-15 17:00:13-debug: refresh db internal success
2025-9-15 17:00:13-debug: asset-db:worker-effect-data-processing (192ms)
2025-9-15 17:00:13-debug: refresh db assets success
2025-9-15 17:00:13-debug: asset-db:refresh-all-database (583ms)
2025-9-15 17:00:18-debug: refresh db internal success
2025-9-15 17:00:18-debug: asset-db:worker-effect-data-processing (145ms)
2025-9-15 17:00:18-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 17:00:18-debug: refresh db assets success
2025-9-15 17:00:18-debug: asset-db:refresh-all-database (322ms)
2025-9-15 17:00:19-debug: Query all assets info in project
2025-9-15 17:00:19-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 17:00:19-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 17:00:19-debug:   Number of all scenes: 2
2025-9-15 17:00:19-debug:   Number of other assets: 2713
2025-9-15 17:00:19-debug:   Number of all scripts: 234
2025-9-15 17:00:19-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 17:00:19-debug: Process: heapTotal 227.67MB heapUsed 201.63MB rss 569.78MB
2025-9-15 17:00:19-debug: 查询 Asset Bundle start0%
2025-9-15 17:00:19-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-15 17:00:19-debug: run build task 查询 Asset Bundle success in 19 ms √3%
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 198.07MB rss 569.85MB
2025-9-15 17:00:19-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 17:00:19-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 17:00:19-debug: init bundle assets
2025-9-15 17:00:19-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 17:00:19-debug: Number of scenes: 1
2025-9-15 17:00:19-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-15 17:00:19-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 199.07MB rss 571.59MB
2025-9-15 17:00:19-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 17:00:19-debug: sort script group...
2025-9-15 17:00:19-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 17:00:19-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 17:00:19-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 17:00:19-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 17:00:19-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 199.23MB rss 571.04MB
2025-9-15 17:00:19-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 17:00:19-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 17:00:19-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 17:00:19-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 17:00:19-debug: builder.tasks.settings.macro start11%
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 199.35MB rss 569.89MB
2025-9-15 17:00:19-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 17:00:19-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 17:00:19-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 17:00:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 199.38MB rss 569.75MB
2025-9-15 17:00:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-15 17:00:19-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-15 17:00:19-debug: custom joint texture layouts start12%
2025-9-15 17:00:19-debug: // ---- build task custom joint texture layouts ----
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 199.46MB rss 569.68MB
2025-9-15 17:00:19-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 17:00:19-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 199.49MB rss 569.69MB
2025-9-15 17:00:19-debug: custom joint physics start12%
2025-9-15 17:00:19-debug: // ---- build task custom joint physics ----
2025-9-15 17:00:19-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 17:00:19-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 17:00:19-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 17:00:19-debug: 填充脚本数据到 settings.json start13%
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 199.52MB rss 569.69MB
2025-9-15 17:00:19-debug: 填充场景数据到 settings.json start13%
2025-9-15 17:00:19-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 17:00:19-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 199.55MB rss 569.69MB
2025-9-15 17:00:19-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 17:00:19-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 17:00:19-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 17:00:19-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 17:00:19-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 199.58MB rss 569.68MB
2025-9-15 17:00:19-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 5 ms √14%
2025-9-15 17:00:19-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 17:00:19-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (5ms)
2025-9-15 17:00:19-debug: Process: heapTotal 227.92MB heapUsed 200.43MB rss 570.55MB
2025-9-15 17:00:19-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 17:00:19-debug: options.md5Cache is false.
2025-9-15 17:00:19-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 17:02:03-debug: refresh db internal success
2025-9-15 17:02:03-debug: asset-db:worker-effect-data-processing (120ms)
2025-9-15 17:02:04-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 17:02:04-debug: refresh db assets success
2025-9-15 17:02:04-debug: asset-db:refresh-all-database (366ms)
2025-9-15 17:02:05-debug: Query all assets info in project
2025-9-15 17:02:05-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 17:02:05-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 17:02:05-debug:   Number of other assets: 2713
2025-9-15 17:02:05-debug:   Number of all scripts: 234
2025-9-15 17:02:05-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 197.30MB rss 565.87MB
2025-9-15 17:02:05-debug:   Number of all scenes: 2
2025-9-15 17:02:05-debug: 查询 Asset Bundle start0%
2025-9-15 17:02:05-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-15 17:02:05-debug: run build task 查询 Asset Bundle success in 21 ms √3%
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 199.92MB rss 566.07MB
2025-9-15 17:02:05-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 17:02:05-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 17:02:05-debug: init bundle assets
2025-9-15 17:02:05-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 17:02:05-debug: Number of scenes: 1
2025-9-15 17:02:05-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (5ms)
2025-9-15 17:02:05-debug: run build task 查询使用的资源以及资源包配置 success in 5 ms √7%
2025-9-15 17:02:05-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 200.92MB rss 566.18MB
2025-9-15 17:02:05-debug: sort script group...
2025-9-15 17:02:05-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 17:02:05-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 17:02:05-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 17:02:05-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 201.07MB rss 566.15MB
2025-9-15 17:02:05-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 17:02:05-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 17:02:05-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 17:02:05-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 201.15MB rss 566.07MB
2025-9-15 17:02:05-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 17:02:05-debug: builder.tasks.settings.macro start11%
2025-9-15 17:02:05-debug: run build task builder.tasks.settings.macro success in 0 ms √11%
2025-9-15 17:02:05-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 201.18MB rss 565.97MB
2025-9-15 17:02:05-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 17:02:05-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 17:02:05-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 17:02:05-debug: // ---- build task custom joint texture layouts ----
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 201.26MB rss 566.02MB
2025-9-15 17:02:05-debug: custom joint texture layouts start12%
2025-9-15 17:02:05-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 17:02:05-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 17:02:05-debug: custom joint physics start12%
2025-9-15 17:02:05-debug: // ---- build task custom joint physics ----
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 201.29MB rss 566.02MB
2025-9-15 17:02:05-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 17:02:05-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 17:02:05-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 17:02:05-debug: 填充脚本数据到 settings.json start13%
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 201.32MB rss 566.02MB
2025-9-15 17:02:05-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 17:02:05-debug: 填充场景数据到 settings.json start13%
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 201.34MB rss 566.02MB
2025-9-15 17:02:05-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 17:02:05-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 17:02:05-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 17:02:05-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 201.37MB rss 566.01MB
2025-9-15 17:02:05-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-15 17:02:05-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-15 17:02:05-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 17:02:05-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 17:02:05-debug: Process: heapTotal 227.95MB heapUsed 202.11MB rss 566.88MB
2025-9-15 17:02:05-debug: options.md5Cache is false.
2025-9-15 17:02:05-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 17:02:05-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 17:02:08-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-15 17:02:08-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 17:02:08-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 17:02:22-debug: refresh db internal success
2025-9-15 17:02:22-debug: asset-db:worker-effect-data-processing (243ms)
2025-9-15 17:02:22-debug: refresh db assets success
2025-9-15 17:02:22-debug: asset-db:refresh-all-database (566ms)
2025-9-15 17:02:36-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-15 17:02:36-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-15 17:02:36-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-15 17:02:38-debug: refresh db internal success
2025-9-15 17:02:38-debug: asset-db:worker-effect-data-processing (141ms)
2025-9-15 17:02:38-debug: refresh db assets success
2025-9-15 17:02:38-debug: asset-db:refresh-all-database (303ms)
2025-9-15 17:02:40-debug: Query all assets info in project
2025-9-15 17:02:40-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 17:02:40-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 17:02:40-debug:   Number of all scenes: 2
2025-9-15 17:02:40-debug:   Number of other assets: 2713
2025-9-15 17:02:40-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 17:02:40-debug: 查询 Asset Bundle start0%
2025-9-15 17:02:40-debug: Process: heapTotal 228.70MB heapUsed 204.52MB rss 570.81MB
2025-9-15 17:02:40-debug:   Number of all scripts: 234
2025-9-15 17:02:41-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-15 17:02:41-debug: run build task 查询 Asset Bundle success in 21 ms √3%
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 201.76MB rss 571.15MB
2025-9-15 17:02:41-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 17:02:41-debug: init bundle assets
2025-9-15 17:02:41-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 17:02:41-debug: Number of scenes: 1
2025-9-15 17:02:41-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 17:02:41-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (9ms)
2025-9-15 17:02:41-debug: run build task 查询使用的资源以及资源包配置 success in 9 ms √7%
2025-9-15 17:02:41-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 202.76MB rss 571.06MB
2025-9-15 17:02:41-debug: sort script group...
2025-9-15 17:02:41-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 17:02:41-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-15 17:02:41-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-15 17:02:41-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 17:02:41-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 202.91MB rss 571.04MB
2025-9-15 17:02:41-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-15 17:02:41-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-15 17:02:41-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 17:02:41-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 17:02:41-debug: builder.tasks.settings.macro start11%
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 202.99MB rss 570.96MB
2025-9-15 17:02:41-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-15 17:02:41-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-15 17:02:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 17:02:41-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 203.02MB rss 570.86MB
2025-9-15 17:02:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-15 17:02:41-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-15 17:02:41-debug: custom joint texture layouts start12%
2025-9-15 17:02:41-debug: // ---- build task custom joint texture layouts ----
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 203.09MB rss 570.91MB
2025-9-15 17:02:41-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 17:02:41-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 17:02:41-debug: // ---- build task custom joint physics ----
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 203.13MB rss 570.91MB
2025-9-15 17:02:41-debug: custom joint physics start12%
2025-9-15 17:02:41-debug: run build task custom joint physics success in 0 ms √13%
2025-9-15 17:02:41-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 203.15MB rss 570.91MB
2025-9-15 17:02:41-debug: 填充脚本数据到 settings.json start13%
2025-9-15 17:02:41-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-15 17:02:41-debug: 填充场景数据到 settings.json start13%
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 203.18MB rss 570.91MB
2025-9-15 17:02:41-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 17:02:41-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-15 17:02:41-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 203.21MB rss 570.90MB
2025-9-15 17:02:41-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 17:02:41-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (5ms)
2025-9-15 17:02:41-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 17:02:41-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 5 ms √14%
2025-9-15 17:02:41-debug: options.md5Cache is false.
2025-9-15 17:02:41-debug: Process: heapTotal 228.95MB heapUsed 204.04MB rss 571.93MB
2025-9-15 17:02:41-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 17:02:41-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-15 17:04:46-debug: refresh db internal success
2025-9-15 17:04:46-debug: asset-db:worker-effect-data-processing (177ms)
2025-9-15 17:04:46-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 17:04:46-debug: refresh db assets success
2025-9-15 17:04:46-debug: asset-db:refresh-all-database (538ms)
2025-9-15 17:04:48-debug: Query all assets info in project
2025-9-15 17:04:48-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 17:04:48-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 17:04:48-debug:   Number of all scenes: 2
2025-9-15 17:04:48-debug:   Number of all scripts: 234
2025-9-15 17:04:48-debug:   Number of other assets: 2713
2025-9-15 17:04:48-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 17:04:48-debug: Process: heapTotal 234.39MB heapUsed 213.24MB rss 577.32MB
2025-9-15 17:04:48-debug: 查询 Asset Bundle start0%
2025-9-15 17:04:48-debug: // ---- build task 查询 Asset Bundle ---- (28ms)
2025-9-15 17:04:48-debug: run build task 查询 Asset Bundle success in 28 ms √3%
2025-9-15 17:04:48-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 17:04:48-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 211.58MB rss 578.15MB
2025-9-15 17:04:48-debug: init bundle assets
2025-9-15 17:04:48-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 17:04:48-debug: Number of scenes: 1
2025-9-15 17:04:48-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 17:04:48-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 212.59MB rss 577.82MB
2025-9-15 17:04:48-debug: sort script group...
2025-9-15 17:04:48-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (7ms)
2025-9-15 17:04:48-debug: run build task 查询使用的资源以及资源包配置 success in 7 ms √7%
2025-9-15 17:04:48-debug: // ---- build task 整理脚本分组与脚本数据 ---- (3ms)
2025-9-15 17:04:48-debug: run build task 整理脚本分组与脚本数据 success in 3 ms √10%
2025-9-15 17:04:48-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 17:04:48-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 212.74MB rss 577.69MB
2025-9-15 17:04:48-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 17:04:48-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 17:04:48-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 17:04:48-debug: builder.tasks.settings.macro start11%
2025-9-15 17:04:48-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 212.82MB rss 577.54MB
2025-9-15 17:04:48-debug: // ---- build task builder.tasks.settings.macro ---- (5ms)
2025-9-15 17:04:48-debug: run build task builder.tasks.settings.macro success in 5 ms √11%
2025-9-15 17:04:48-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 17:04:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 212.85MB rss 577.43MB
2025-9-15 17:04:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-9-15 17:04:48-debug: run build task 整理部分构建选项内数据到 settings.json success in 6 ms √12%
2025-9-15 17:04:48-debug: custom joint texture layouts start12%
2025-9-15 17:04:48-debug: // ---- build task custom joint texture layouts ----
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 212.92MB rss 577.45MB
2025-9-15 17:04:48-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-15 17:04:48-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-15 17:04:48-debug: custom joint physics start12%
2025-9-15 17:04:48-debug: // ---- build task custom joint physics ----
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 212.95MB rss 577.46MB
2025-9-15 17:04:48-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 17:04:48-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 17:04:48-debug: 填充脚本数据到 settings.json start13%
2025-9-15 17:04:48-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 212.98MB rss 577.46MB
2025-9-15 17:04:48-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 17:04:48-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 17:04:48-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 17:04:48-debug: 填充场景数据到 settings.json start13%
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 213.01MB rss 577.46MB
2025-9-15 17:04:48-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 17:04:48-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 17:04:48-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 17:04:48-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 213.04MB rss 577.46MB
2025-9-15 17:04:48-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (5ms)
2025-9-15 17:04:48-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 17:04:48-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 17:04:48-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 5 ms √14%
2025-9-15 17:04:48-debug: Process: heapTotal 234.64MB heapUsed 213.68MB rss 577.46MB
2025-9-15 17:04:48-debug: options.md5Cache is false.
2025-9-15 17:04:48-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 17:04:48-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 17:05:10-debug: refresh db internal success
2025-9-15 17:05:11-debug: asset-db:worker-effect-data-processing (140ms)
2025-9-15 17:05:11-debug: refresh db assets success
2025-9-15 17:05:11-debug: asset-db:refresh-all-database (307ms)
2025-9-15 17:05:29-debug: refresh db internal success
2025-9-15 17:05:29-debug: asset-db:worker-effect-data-processing (111ms)
2025-9-15 17:05:30-debug: refresh db assets success
2025-9-15 17:05:30-debug: asset-db:refresh-all-database (299ms)
2025-9-15 17:11:02-debug: refresh db internal success
2025-9-15 17:11:02-debug: asset-db:worker-effect-data-processing (258ms)
2025-9-15 17:11:02-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 17:11:02-debug: refresh db assets success
2025-9-15 17:11:02-debug: asset-db:refresh-all-database (623ms)
2025-9-15 17:11:04-debug: Query all assets info in project
2025-9-15 17:11:04-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 17:11:04-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 17:11:04-debug:   Number of all scenes: 2
2025-9-15 17:11:04-debug: 查询 Asset Bundle start0%
2025-9-15 17:11:04-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 17:11:04-debug: Process: heapTotal 227.68MB heapUsed 202.75MB rss 231.14MB
2025-9-15 17:11:04-debug:   Number of all scripts: 234
2025-9-15 17:11:04-debug:   Number of other assets: 2713
2025-9-15 17:11:04-debug: // ---- build task 查询 Asset Bundle ---- (27ms)
2025-9-15 17:11:04-debug: run build task 查询 Asset Bundle success in 27 ms √3%
2025-9-15 17:11:04-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 17:11:04-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 200.24MB rss 231.36MB
2025-9-15 17:11:04-debug: init bundle assets
2025-9-15 17:11:04-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 17:11:04-debug: Number of scenes: 1
2025-9-15 17:11:04-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (7ms)
2025-9-15 17:11:04-debug: run build task 查询使用的资源以及资源包配置 success in 7 ms √7%
2025-9-15 17:11:04-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 17:11:04-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 201.26MB rss 230.45MB
2025-9-15 17:11:04-debug: sort script group...
2025-9-15 17:11:04-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-15 17:11:04-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-15 17:11:04-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 17:11:04-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 201.41MB rss 230.43MB
2025-9-15 17:11:04-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 17:11:04-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 17:11:04-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 17:11:04-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 17:11:04-debug: builder.tasks.settings.macro start11%
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 201.48MB rss 230.38MB
2025-9-15 17:11:04-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-15 17:11:04-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-15 17:11:04-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 17:11:04-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 201.52MB rss 230.25MB
2025-9-15 17:11:04-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-15 17:11:04-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-15 17:11:04-debug: custom joint texture layouts start12%
2025-9-15 17:11:04-debug: // ---- build task custom joint texture layouts ----
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 201.59MB rss 230.30MB
2025-9-15 17:11:04-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 17:11:04-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 17:11:04-debug: custom joint physics start12%
2025-9-15 17:11:04-debug: // ---- build task custom joint physics ----
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 201.63MB rss 230.30MB
2025-9-15 17:11:04-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 17:11:04-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 17:11:04-debug: 填充脚本数据到 settings.json start13%
2025-9-15 17:11:04-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 201.65MB rss 230.30MB
2025-9-15 17:11:04-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 17:11:04-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 17:11:04-debug: 填充场景数据到 settings.json start13%
2025-9-15 17:11:04-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 201.69MB rss 230.31MB
2025-9-15 17:11:04-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-15 17:11:04-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-15 17:11:04-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 17:11:04-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 201.72MB rss 230.30MB
2025-9-15 17:11:04-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (9ms)
2025-9-15 17:11:04-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 9 ms √14%
2025-9-15 17:11:04-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 17:11:04-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 17:11:04-debug: Process: heapTotal 227.93MB heapUsed 202.57MB rss 231.14MB
2025-9-15 17:11:04-debug: options.md5Cache is false.
2025-9-15 17:11:04-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 17:11:04-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-15 17:27:00-debug: refresh db internal success
2025-9-15 17:27:00-debug: asset-db:worker-effect-data-processing (279ms)
2025-9-15 17:27:01-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-15 17:27:01-debug: refresh db assets success
2025-9-15 17:27:01-debug: asset-db:refresh-all-database (632ms)
2025-9-15 17:27:03-debug: Query all assets info in project
2025-9-15 17:27:03-debug: BuildAssetLibrary query-assets with assets 3094
2025-9-15 17:27:03-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-15 17:27:03-debug:   Number of all scenes: 2
2025-9-15 17:27:03-debug:   Number of other assets: 2713
2025-9-15 17:27:03-debug: // ---- build task 查询 Asset Bundle ----
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 202.58MB rss 225.84MB
2025-9-15 17:27:03-debug:   Number of all scripts: 234
2025-9-15 17:27:03-debug: 查询 Asset Bundle start0%
2025-9-15 17:27:03-debug: // ---- build task 查询 Asset Bundle ---- (31ms)
2025-9-15 17:27:03-debug: run build task 查询 Asset Bundle success in 31 ms √3%
2025-9-15 17:27:03-debug: 查询使用的资源以及资源包配置 start3%
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 198.41MB rss 229.08MB
2025-9-15 17:27:03-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-15 17:27:03-debug: init bundle assets
2025-9-15 17:27:03-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-15 17:27:03-debug: Number of scenes: 1
2025-9-15 17:27:03-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (10ms)
2025-9-15 17:27:03-debug: run build task 查询使用的资源以及资源包配置 success in 10 ms √7%
2025-9-15 17:27:03-debug: 整理脚本分组与脚本数据 start7%
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 199.41MB rss 229.10MB
2025-9-15 17:27:03-debug: sort script group...
2025-9-15 17:27:03-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-15 17:27:03-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-15 17:27:03-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-15 17:27:03-debug: 初始化 settings.json 与 config.json start10%
2025-9-15 17:27:03-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 199.56MB rss 229.08MB
2025-9-15 17:27:03-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-15 17:27:03-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-15 17:27:03-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-15 17:27:03-debug: builder.tasks.settings.macro start11%
2025-9-15 17:27:03-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 199.63MB rss 229.00MB
2025-9-15 17:27:03-debug: // ---- build task builder.tasks.settings.macro ---- (7ms)
2025-9-15 17:27:03-debug: run build task builder.tasks.settings.macro success in 7 ms √11%
2025-9-15 17:27:03-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-15 17:27:03-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 199.67MB rss 228.90MB
2025-9-15 17:27:03-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-15 17:27:03-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-15 17:27:03-debug: // ---- build task custom joint texture layouts ----
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 199.74MB rss 228.93MB
2025-9-15 17:27:03-debug: custom joint texture layouts start12%
2025-9-15 17:27:03-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-15 17:27:03-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-15 17:27:03-debug: custom joint physics start12%
2025-9-15 17:27:03-debug: // ---- build task custom joint physics ----
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 199.78MB rss 228.94MB
2025-9-15 17:27:03-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-15 17:27:03-debug: run build task custom joint physics success in 1 ms √13%
2025-9-15 17:27:03-debug: 填充脚本数据到 settings.json start13%
2025-9-15 17:27:03-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 199.80MB rss 228.94MB
2025-9-15 17:27:03-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-15 17:27:03-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-15 17:27:03-debug: 填充场景数据到 settings.json start13%
2025-9-15 17:27:03-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 199.83MB rss 228.94MB
2025-9-15 17:27:03-debug: // ---- build task 填充场景数据到 settings.json ---- (2ms)
2025-9-15 17:27:03-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 199.86MB rss 228.94MB
2025-9-15 17:27:03-debug: run build task 填充场景数据到 settings.json success in 2 ms √14%
2025-9-15 17:27:03-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-15 17:27:03-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (10ms)
2025-9-15 17:27:03-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 10 ms √14%
2025-9-15 17:27:03-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-15 17:27:03-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-15 17:27:03-debug: Process: heapTotal 227.71MB heapUsed 200.72MB rss 229.81MB
2025-9-15 17:27:03-debug: options.md5Cache is false.
2025-9-15 17:27:03-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-15 17:27:03-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 10:16:36-debug: refresh db internal success
2025-9-16 10:16:36-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\game\model\ElixirUnsealCfg.ts
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:16:37-debug: asset-db:worker-effect-data-processing (778ms)
2025-9-16 10:16:37-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\resources\config\game\elixir_unseal.json
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:16:37-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\resources\config\game
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:16:37-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\common\Config.ts
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:16:37-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\game\model
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:16:37-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:16:37-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\hud\ElixirNode.ts
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:16:37-debug: refresh db assets success
2025-9-16 10:16:37-debug: asset-db:refresh-all-database (1840ms)
2025-9-16 10:16:40-debug: Query all assets info in project
2025-9-16 10:16:40-debug: BuildAssetLibrary query-assets with assets 3096
2025-9-16 10:16:40-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:16:40-debug: 查询 Asset Bundle start0%
2025-9-16 10:16:40-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:16:40-debug:   Number of all scripts: 235
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 201.26MB rss 223.81MB
2025-9-16 10:16:40-debug:   Number of other assets: 2714
2025-9-16 10:16:40-debug:   Number of all scenes: 2
2025-9-16 10:16:40-debug: // ---- build task 查询 Asset Bundle ---- (32ms)
2025-9-16 10:16:40-debug: run build task 查询 Asset Bundle success in 32 ms √3%
2025-9-16 10:16:40-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:16:40-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 203.59MB rss 225.00MB
2025-9-16 10:16:40-debug: init bundle assets
2025-9-16 10:16:40-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:16:40-debug: Number of scenes: 1
2025-9-16 10:16:40-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (14ms)
2025-9-16 10:16:40-debug: run build task 查询使用的资源以及资源包配置 success in 14 ms √7%
2025-9-16 10:16:40-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:16:40-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 204.59MB rss 225.00MB
2025-9-16 10:16:40-debug: sort script group...
2025-9-16 10:16:40-debug: // ---- build task 整理脚本分组与脚本数据 ---- (3ms)
2025-9-16 10:16:40-debug: run build task 整理脚本分组与脚本数据 success in 3 ms √10%
2025-9-16 10:16:40-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:16:40-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 204.74MB rss 224.98MB
2025-9-16 10:16:40-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-16 10:16:40-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-16 10:16:40-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:16:40-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 204.81MB rss 224.94MB
2025-9-16 10:16:40-debug: builder.tasks.settings.macro start11%
2025-9-16 10:16:40-debug: // ---- build task builder.tasks.settings.macro ---- (12ms)
2025-9-16 10:16:40-debug: run build task builder.tasks.settings.macro success in 12 ms √11%
2025-9-16 10:16:40-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:16:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 199.22MB rss 224.68MB
2025-9-16 10:16:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (26ms)
2025-9-16 10:16:40-debug: run build task 整理部分构建选项内数据到 settings.json success in 26 ms √12%
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 199.29MB rss 224.75MB
2025-9-16 10:16:40-debug: custom joint texture layouts start12%
2025-9-16 10:16:40-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:16:40-debug: // ---- build task custom joint texture layouts ---- (6ms)
2025-9-16 10:16:40-debug: run build task custom joint texture layouts success in 6 ms √12%
2025-9-16 10:16:40-debug: custom joint physics start12%
2025-9-16 10:16:40-debug: // ---- build task custom joint physics ----
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 199.33MB rss 224.61MB
2025-9-16 10:16:40-debug: // ---- build task custom joint physics ---- (3ms)
2025-9-16 10:16:40-debug: run build task custom joint physics success in 3 ms √13%
2025-9-16 10:16:40-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:16:40-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 199.35MB rss 224.62MB
2025-9-16 10:16:40-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-16 10:16:40-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-16 10:16:40-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:16:40-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 199.38MB rss 224.62MB
2025-9-16 10:16:40-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-16 10:16:40-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-16 10:16:40-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:16:40-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 199.41MB rss 224.62MB
2025-9-16 10:16:40-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (15ms)
2025-9-16 10:16:40-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 15 ms √14%
2025-9-16 10:16:40-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:16:40-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:16:40-debug: Process: heapTotal 228.53MB heapUsed 200.25MB rss 225.39MB
2025-9-16 10:16:40-debug: options.md5Cache is false.
2025-9-16 10:16:40-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (2ms)
2025-9-16 10:16:40-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 2 ms √15%
2025-9-16 10:17:08-debug: refresh db internal success
2025-9-16 10:17:08-debug: asset-db:worker-effect-data-processing (109ms)
2025-9-16 10:17:08-debug: refresh db assets success
2025-9-16 10:17:08-debug: asset-db:refresh-all-database (269ms)
2025-9-16 10:17:19-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-16 10:17:19-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:17:19-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-16 10:17:22-debug: Query all assets info in project
2025-9-16 10:17:22-debug: BuildAssetLibrary query-assets with assets 3096
2025-9-16 10:17:22-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:17:22-debug:   Number of all scripts: 235
2025-9-16 10:17:22-debug:   Number of other assets: 2714
2025-9-16 10:17:22-debug:   Number of all scenes: 2
2025-9-16 10:17:22-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:17:22-debug: Process: heapTotal 225.20MB heapUsed 211.95MB rss 225.87MB
2025-9-16 10:17:22-debug: 查询 Asset Bundle start0%
2025-9-16 10:17:22-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-16 10:17:22-debug: run build task 查询 Asset Bundle success in 20 ms √3%
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 213.17MB rss 230.48MB
2025-9-16 10:17:22-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:17:22-debug: init bundle assets
2025-9-16 10:17:22-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:17:22-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:17:22-debug: Number of scenes: 1
2025-9-16 10:17:22-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (5ms)
2025-9-16 10:17:22-debug: run build task 查询使用的资源以及资源包配置 success in 5 ms √7%
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 214.16MB rss 230.14MB
2025-9-16 10:17:22-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:17:22-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:17:22-debug: sort script group...
2025-9-16 10:17:22-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 10:17:22-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:17:22-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 214.31MB rss 230.02MB
2025-9-16 10:17:22-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 10:17:22-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-16 10:17:22-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:17:22-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-16 10:17:22-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 214.39MB rss 229.88MB
2025-9-16 10:17:22-debug: builder.tasks.settings.macro start11%
2025-9-16 10:17:22-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-16 10:17:22-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 214.42MB rss 229.75MB
2025-9-16 10:17:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:17:22-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:17:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-16 10:17:22-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-16 10:17:22-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 214.49MB rss 229.79MB
2025-9-16 10:17:22-debug: custom joint texture layouts start12%
2025-9-16 10:17:22-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 10:17:22-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 214.52MB rss 229.79MB
2025-9-16 10:17:22-debug: custom joint physics start12%
2025-9-16 10:17:22-debug: // ---- build task custom joint physics ----
2025-9-16 10:17:22-debug: run build task custom joint physics success in 0 ms √13%
2025-9-16 10:17:22-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 214.55MB rss 229.79MB
2025-9-16 10:17:22-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:17:22-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 10:17:22-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:17:22-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 214.57MB rss 229.80MB
2025-9-16 10:17:22-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-16 10:17:22-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:17:22-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 214.60MB rss 229.80MB
2025-9-16 10:17:22-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:17:22-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (4ms)
2025-9-16 10:17:22-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 4 ms √14%
2025-9-16 10:17:22-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:17:22-debug: Process: heapTotal 229.45MB heapUsed 215.29MB rss 229.82MB
2025-9-16 10:17:22-debug: options.md5Cache is false.
2025-9-16 10:17:22-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-16 10:17:42-debug: Query all assets info in project
2025-9-16 10:17:42-debug: BuildAssetLibrary query-assets with assets 3096
2025-9-16 10:17:42-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:17:42-debug:   Number of all scenes: 2
2025-9-16 10:17:42-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:17:42-debug:   Number of all scripts: 235
2025-9-16 10:17:42-debug: Process: heapTotal 226.20MB heapUsed 215.62MB rss 224.73MB
2025-9-16 10:17:42-debug:   Number of other assets: 2714
2025-9-16 10:17:42-debug: 查询 Asset Bundle start0%
2025-9-16 10:17:42-debug: // ---- build task 查询 Asset Bundle ---- (33ms)
2025-9-16 10:17:42-debug: run build task 查询 Asset Bundle success in 33 ms √3%
2025-9-16 10:17:42-debug: Process: heapTotal 231.70MB heapUsed 216.85MB rss 228.87MB
2025-9-16 10:17:42-debug: init bundle assets
2025-9-16 10:17:42-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:17:42-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:17:42-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:17:42-debug: Number of scenes: 1
2025-9-16 10:17:42-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:17:42-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (81ms)
2025-9-16 10:17:42-debug: run build task 查询使用的资源以及资源包配置 success in 81 ms √7%
2025-9-16 10:17:42-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:17:42-debug: sort script group...
2025-9-16 10:17:42-debug: Process: heapTotal 220.26MB heapUsed 194.60MB rss 212.73MB
2025-9-16 10:17:42-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:17:42-debug: Process: heapTotal 220.26MB heapUsed 194.74MB rss 212.70MB
2025-9-16 10:17:42-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-16 10:17:42-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-16 10:17:42-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:17:42-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-16 10:17:42-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-16 10:17:42-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:17:42-debug: builder.tasks.settings.macro start11%
2025-9-16 10:17:42-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:17:42-debug: Process: heapTotal 220.26MB heapUsed 194.82MB rss 212.64MB
2025-9-16 10:17:42-debug: run build task builder.tasks.settings.macro success in 3 ms √11%
2025-9-16 10:17:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:17:42-debug: // ---- build task builder.tasks.settings.macro ---- (3ms)
2025-9-16 10:17:42-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:17:42-debug: Process: heapTotal 220.26MB heapUsed 194.86MB rss 212.52MB
2025-9-16 10:17:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-16 10:17:42-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-16 10:17:42-debug: custom joint texture layouts start12%
2025-9-16 10:17:42-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:17:42-debug: Process: heapTotal 220.26MB heapUsed 194.93MB rss 212.57MB
2025-9-16 10:17:42-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-16 10:17:42-debug: custom joint physics start12%
2025-9-16 10:17:42-debug: // ---- build task custom joint physics ----
2025-9-16 10:17:42-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-16 10:17:42-debug: Process: heapTotal 220.26MB heapUsed 194.97MB rss 212.57MB
2025-9-16 10:17:42-debug: // ---- build task custom joint physics ---- (2ms)
2025-9-16 10:17:42-debug: run build task custom joint physics success in 2 ms √13%
2025-9-16 10:17:42-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:17:42-debug: Process: heapTotal 220.26MB heapUsed 195.00MB rss 212.57MB
2025-9-16 10:17:42-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:17:42-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-16 10:17:42-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-16 10:17:42-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:17:42-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:17:42-debug: Process: heapTotal 220.26MB heapUsed 195.03MB rss 212.58MB
2025-9-16 10:17:42-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-16 10:17:42-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-16 10:17:42-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:17:42-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:17:42-debug: Process: heapTotal 220.26MB heapUsed 195.06MB rss 212.58MB
2025-9-16 10:17:42-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (9ms)
2025-9-16 10:17:42-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 9 ms √14%
2025-9-16 10:17:42-debug: options.md5Cache is false.
2025-9-16 10:17:42-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:17:42-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:17:42-debug: Process: heapTotal 220.26MB heapUsed 195.90MB rss 213.53MB
2025-9-16 10:17:42-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-16 10:17:42-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 10:18:27-debug: refresh db internal success
2025-9-16 10:18:27-debug: asset-db:worker-effect-data-processing (174ms)
2025-9-16 10:18:27-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\game\model\ElixirUnsealCfg.ts
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:18:28-debug: refresh db assets success
2025-9-16 10:18:28-debug: asset-db:refresh-all-database (601ms)
2025-9-16 10:18:31-debug: Query all assets info in project
2025-9-16 10:18:31-debug: BuildAssetLibrary query-assets with assets 3096
2025-9-16 10:18:31-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:18:31-debug:   Number of all scenes: 2
2025-9-16 10:18:31-debug:   Number of all scripts: 235
2025-9-16 10:18:31-debug:   Number of other assets: 2714
2025-9-16 10:18:31-debug: 查询 Asset Bundle start0%
2025-9-16 10:18:31-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 201.20MB rss 224.89MB
2025-9-16 10:18:31-debug: // ---- build task 查询 Asset Bundle ---- (31ms)
2025-9-16 10:18:31-debug: run build task 查询 Asset Bundle success in 31 ms √3%
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 196.76MB rss 224.99MB
2025-9-16 10:18:31-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:18:31-debug: init bundle assets
2025-9-16 10:18:31-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:18:31-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:18:31-debug: Number of scenes: 1
2025-9-16 10:18:31-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (12ms)
2025-9-16 10:18:31-debug: run build task 查询使用的资源以及资源包配置 success in 12 ms √7%
2025-9-16 10:18:31-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:18:31-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 197.77MB rss 226.65MB
2025-9-16 10:18:31-debug: sort script group...
2025-9-16 10:18:31-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 10:18:31-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 10:18:31-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:18:31-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 197.92MB rss 226.98MB
2025-9-16 10:18:31-debug: // ---- build task 初始化 settings.json 与 config.json ---- (3ms)
2025-9-16 10:18:31-debug: run build task 初始化 settings.json 与 config.json success in 3 ms √11%
2025-9-16 10:18:31-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:18:31-debug: builder.tasks.settings.macro start11%
2025-9-16 10:18:31-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 198.21MB rss 225.17MB
2025-9-16 10:18:31-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-16 10:18:31-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-16 10:18:31-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:18:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 198.24MB rss 225.05MB
2025-9-16 10:18:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-16 10:18:31-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-16 10:18:31-debug: custom joint texture layouts start12%
2025-9-16 10:18:31-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 198.31MB rss 224.92MB
2025-9-16 10:18:31-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-16 10:18:31-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-16 10:18:31-debug: custom joint physics start12%
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 198.34MB rss 224.93MB
2025-9-16 10:18:31-debug: // ---- build task custom joint physics ----
2025-9-16 10:18:31-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-16 10:18:31-debug: run build task custom joint physics success in 1 ms √13%
2025-9-16 10:18:31-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:18:31-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 198.37MB rss 224.93MB
2025-9-16 10:18:31-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 10:18:31-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:18:31-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 198.40MB rss 224.93MB
2025-9-16 10:18:31-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-16 10:18:31-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-16 10:18:31-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:18:31-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 198.42MB rss 224.93MB
2025-9-16 10:18:31-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (8ms)
2025-9-16 10:18:31-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 8 ms √14%
2025-9-16 10:18:31-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:18:31-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:18:31-debug: Process: heapTotal 228.01MB heapUsed 199.28MB rss 225.71MB
2025-9-16 10:18:31-debug: options.md5Cache is false.
2025-9-16 10:18:31-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-16 10:18:31-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 10:20:26-debug: refresh db internal success
2025-9-16 10:20:27-debug: asset-db:worker-effect-data-processing (138ms)
2025-9-16 10:20:27-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\game\model\ElixirUnsealCfg.ts
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:20:27-debug: refresh db assets success
2025-9-16 10:20:27-debug: asset-db:refresh-all-database (299ms)
2025-9-16 10:20:31-debug: Query all assets info in project
2025-9-16 10:20:31-debug: BuildAssetLibrary query-assets with assets 3096
2025-9-16 10:20:31-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:20:31-debug:   Number of all scenes: 2
2025-9-16 10:20:31-debug:   Number of all scripts: 235
2025-9-16 10:20:31-debug:   Number of other assets: 2714
2025-9-16 10:20:31-debug: 查询 Asset Bundle start0%
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 201.41MB rss 226.05MB
2025-9-16 10:20:31-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:20:31-debug: run build task 查询 Asset Bundle success in 31 ms √3%
2025-9-16 10:20:31-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:20:31-debug: // ---- build task 查询 Asset Bundle ---- (31ms)
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 197.37MB rss 227.14MB
2025-9-16 10:20:31-debug: init bundle assets
2025-9-16 10:20:31-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:20:31-debug: Number of scenes: 1
2025-9-16 10:20:31-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:20:31-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (7ms)
2025-9-16 10:20:31-debug: run build task 查询使用的资源以及资源包配置 success in 7 ms √7%
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 198.37MB rss 227.11MB
2025-9-16 10:20:31-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:20:31-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:20:31-debug: sort script group...
2025-9-16 10:20:31-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 10:20:31-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:20:31-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:20:31-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 198.53MB rss 227.32MB
2025-9-16 10:20:31-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-16 10:20:31-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-16 10:20:31-debug: builder.tasks.settings.macro start11%
2025-9-16 10:20:31-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:20:31-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 198.61MB rss 227.13MB
2025-9-16 10:20:31-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-16 10:20:31-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-16 10:20:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:20:31-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 198.64MB rss 227.00MB
2025-9-16 10:20:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-16 10:20:31-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 198.72MB rss 226.92MB
2025-9-16 10:20:31-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:20:31-debug: custom joint texture layouts start12%
2025-9-16 10:20:31-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-16 10:20:31-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-16 10:20:31-debug: // ---- build task custom joint physics ----
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 198.75MB rss 226.93MB
2025-9-16 10:20:31-debug: custom joint physics start12%
2025-9-16 10:20:31-debug: run build task custom joint physics success in 0 ms √13%
2025-9-16 10:20:31-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:20:31-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 198.78MB rss 226.93MB
2025-9-16 10:20:31-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-16 10:20:31-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-16 10:20:31-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 198.81MB rss 226.93MB
2025-9-16 10:20:31-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:20:31-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-16 10:20:31-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:20:31-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 198.83MB rss 226.93MB
2025-9-16 10:20:31-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 5 ms √14%
2025-9-16 10:20:31-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (5ms)
2025-9-16 10:20:31-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:20:31-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:20:31-debug: Process: heapTotal 228.85MB heapUsed 199.69MB rss 227.95MB
2025-9-16 10:20:31-debug: options.md5Cache is false.
2025-9-16 10:20:31-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-16 10:21:48-debug: refresh db internal success
2025-9-16 10:21:48-debug: asset-db:worker-effect-data-processing (175ms)
2025-9-16 10:21:48-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:21:48-debug: refresh db assets success
2025-9-16 10:21:48-debug: asset-db:refresh-all-database (534ms)
2025-9-16 10:21:50-debug: Query all assets info in project
2025-9-16 10:21:50-debug: BuildAssetLibrary query-assets with assets 3096
2025-9-16 10:21:50-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:21:50-debug:   Number of other assets: 2714
2025-9-16 10:21:50-debug: 查询 Asset Bundle start0%
2025-9-16 10:21:50-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:21:50-debug: Process: heapTotal 232.45MB heapUsed 211.57MB rss 235.04MB
2025-9-16 10:21:50-debug:   Number of all scripts: 235
2025-9-16 10:21:50-debug:   Number of all scenes: 2
2025-9-16 10:21:50-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-9-16 10:21:50-debug: run build task 查询 Asset Bundle success in 26 ms √3%
2025-9-16 10:21:50-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 210.36MB rss 234.71MB
2025-9-16 10:21:50-debug: init bundle assets
2025-9-16 10:21:50-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:21:50-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:21:50-debug: Number of scenes: 1
2025-9-16 10:21:50-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-16 10:21:50-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:21:50-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 211.37MB rss 234.49MB
2025-9-16 10:21:50-debug: sort script group...
2025-9-16 10:21:50-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-16 10:21:50-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 10:21:50-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 211.52MB rss 234.36MB
2025-9-16 10:21:50-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:21:50-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:21:50-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-16 10:21:50-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-16 10:21:50-debug: builder.tasks.settings.macro start11%
2025-9-16 10:21:50-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 211.59MB rss 234.24MB
2025-9-16 10:21:50-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:21:50-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-16 10:21:50-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 211.63MB rss 234.10MB
2025-9-16 10:21:50-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:21:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:21:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-16 10:21:50-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-16 10:21:50-debug: custom joint texture layouts start12%
2025-9-16 10:21:50-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 211.70MB rss 234.13MB
2025-9-16 10:21:50-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 10:21:50-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 10:21:50-debug: custom joint physics start12%
2025-9-16 10:21:50-debug: // ---- build task custom joint physics ----
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 211.73MB rss 234.13MB
2025-9-16 10:21:50-debug: run build task custom joint physics success in 0 ms √13%
2025-9-16 10:21:50-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 211.75MB rss 234.13MB
2025-9-16 10:21:50-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:21:50-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-16 10:21:50-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-16 10:21:50-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:21:50-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 211.78MB rss 234.14MB
2025-9-16 10:21:50-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-16 10:21:50-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 211.81MB rss 234.14MB
2025-9-16 10:21:50-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:21:50-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (4ms)
2025-9-16 10:21:50-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 4 ms √14%
2025-9-16 10:21:50-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:21:50-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:21:50-debug: Process: heapTotal 232.70MB heapUsed 212.50MB rss 234.14MB
2025-9-16 10:21:50-debug: options.md5Cache is false.
2025-9-16 10:21:50-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-16 10:21:50-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 10:24:27-debug: refresh db internal success
2025-9-16 10:24:27-debug: asset-db:worker-effect-data-processing (273ms)
2025-9-16 10:24:27-debug: refresh db assets success
2025-9-16 10:24:27-debug: asset-db:refresh-all-database (747ms)
2025-9-16 10:24:36-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test...
2025-9-16 10:24:36-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:24:36-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets success
2025-9-16 10:24:44-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene...
2025-9-16 10:24:44-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:24:44-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\test success
2025-9-16 10:24:44-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:24:51-debug: refresh db internal success
2025-9-16 10:24:51-debug: asset-db:worker-effect-data-processing (167ms)
2025-9-16 10:24:52-debug: refresh db assets success
2025-9-16 10:24:52-debug: asset-db:refresh-all-database (355ms)
2025-9-16 10:24:52-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.atlas...
2025-9-16 10:24:52-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.atlas
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:24:52-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.json...
2025-9-16 10:24:52-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.png...
2025-9-16 10:24:52-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.json
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:24:52-log: UUID is initialized for "%s".
F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.png
2025-9-16 10:24:52-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.png
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:24:52-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:24:52-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:24:52-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\test success
2025-9-16 10:24:52-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\test success
2025-9-16 10:24:52-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\test success
2025-9-16 10:24:52-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.json
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:24:52-log: UUID is initialized for "%s".
F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.png
2025-9-16 10:24:53-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:25:19-debug: Query all assets info in project
2025-9-16 10:25:19-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 10:25:19-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:25:19-debug:   Number of all scenes: 3
2025-9-16 10:25:19-debug:   Number of other assets: 2719
2025-9-16 10:25:19-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:25:19-debug: Process: heapTotal 216.05MB heapUsed 201.21MB rss 218.81MB
2025-9-16 10:25:19-debug:   Number of all scripts: 235
2025-9-16 10:25:19-debug: 查询 Asset Bundle start0%
2025-9-16 10:25:19-debug: // ---- build task 查询 Asset Bundle ---- (27ms)
2025-9-16 10:25:19-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:25:19-debug: run build task 查询 Asset Bundle success in 27 ms √3%
2025-9-16 10:25:19-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:25:19-debug: Process: heapTotal 217.30MB heapUsed 202.47MB rss 219.31MB
2025-9-16 10:25:19-debug: init bundle assets
2025-9-16 10:25:19-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:25:19-debug: Number of scenes: 2
2025-9-16 10:25:19-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (10ms)
2025-9-16 10:25:19-debug: run build task 查询使用的资源以及资源包配置 success in 10 ms √7%
2025-9-16 10:25:19-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:25:19-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:25:19-debug: Process: heapTotal 217.30MB heapUsed 202.80MB rss 219.50MB
2025-9-16 10:25:19-debug: sort script group...
2025-9-16 10:25:19-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 10:25:19-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 10:25:19-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:25:19-debug: Process: heapTotal 219.30MB heapUsed 202.42MB rss 219.69MB
2025-9-16 10:25:19-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:25:19-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-16 10:25:19-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-16 10:25:19-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:25:19-debug: builder.tasks.settings.macro start11%
2025-9-16 10:25:19-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:25:19-debug: Process: heapTotal 219.30MB heapUsed 202.50MB rss 219.59MB
2025-9-16 10:25:19-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-16 10:25:19-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-16 10:25:19-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:25:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:25:19-debug: Process: heapTotal 219.30MB heapUsed 202.53MB rss 219.50MB
2025-9-16 10:25:19-debug: custom joint texture layouts start12%
2025-9-16 10:25:19-debug: run build task 整理部分构建选项内数据到 settings.json success in 5 ms √12%
2025-9-16 10:25:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-9-16 10:25:19-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:25:19-debug: Process: heapTotal 219.30MB heapUsed 202.60MB rss 219.46MB
2025-9-16 10:25:19-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 10:25:19-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 10:25:19-debug: // ---- build task custom joint physics ----
2025-9-16 10:25:19-debug: Process: heapTotal 219.30MB heapUsed 202.64MB rss 219.46MB
2025-9-16 10:25:19-debug: custom joint physics start12%
2025-9-16 10:25:19-debug: run build task custom joint physics success in 0 ms √13%
2025-9-16 10:25:19-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:25:19-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:25:19-debug: Process: heapTotal 219.30MB heapUsed 202.66MB rss 219.46MB
2025-9-16 10:25:19-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 10:25:19-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:25:19-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:25:19-debug: Process: heapTotal 219.30MB heapUsed 202.68MB rss 219.46MB
2025-9-16 10:25:19-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-16 10:25:19-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:25:19-debug: Process: heapTotal 219.30MB heapUsed 202.71MB rss 219.46MB
2025-9-16 10:25:19-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:25:19-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-16 10:25:19-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-16 10:25:19-debug: Process: heapTotal 219.30MB heapUsed 203.56MB rss 220.57MB
2025-9-16 10:25:19-debug: options.md5Cache is false.
2025-9-16 10:25:19-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:25:19-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:25:19-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-16 10:25:23-debug: Query all assets info in project
2025-9-16 10:25:23-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 10:25:23-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:25:23-debug:   Number of all scripts: 235
2025-9-16 10:25:23-debug:   Number of other assets: 2719
2025-9-16 10:25:23-debug: 查询 Asset Bundle start0%
2025-9-16 10:25:23-debug: Process: heapTotal 223.30MB heapUsed 206.21MB rss 225.67MB
2025-9-16 10:25:23-debug:   Number of all scenes: 3
2025-9-16 10:25:23-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:25:23-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-16 10:25:23-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:25:23-debug: run build task 查询 Asset Bundle success in 22 ms √3%
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 208.91MB rss 226.95MB
2025-9-16 10:25:23-debug: init bundle assets
2025-9-16 10:25:23-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:25:23-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:25:23-debug: Number of scenes: 2
2025-9-16 10:25:23-debug: run build task 查询使用的资源以及资源包配置 success in 4 ms √7%
2025-9-16 10:25:23-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (4ms)
2025-9-16 10:25:23-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:25:23-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 209.90MB rss 227.89MB
2025-9-16 10:25:23-debug: sort script group...
2025-9-16 10:25:23-debug: run build task 整理脚本分组与脚本数据 success in 0 ms √10%
2025-9-16 10:25:23-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 210.04MB rss 228.02MB
2025-9-16 10:25:23-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:25:23-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-16 10:25:23-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-16 10:25:23-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:25:23-debug: builder.tasks.settings.macro start11%
2025-9-16 10:25:23-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 210.14MB rss 227.07MB
2025-9-16 10:25:23-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-16 10:25:23-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-16 10:25:23-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:25:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 210.17MB rss 226.98MB
2025-9-16 10:25:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-16 10:25:23-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-16 10:25:23-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 210.24MB rss 226.86MB
2025-9-16 10:25:23-debug: custom joint texture layouts start12%
2025-9-16 10:25:23-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 10:25:23-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 210.27MB rss 226.86MB
2025-9-16 10:25:23-debug: custom joint physics start12%
2025-9-16 10:25:23-debug: // ---- build task custom joint physics ----
2025-9-16 10:25:23-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-16 10:25:23-debug: run build task custom joint physics success in 1 ms √13%
2025-9-16 10:25:23-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:25:23-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 210.30MB rss 226.86MB
2025-9-16 10:25:23-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 10:25:23-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 210.32MB rss 227.12MB
2025-9-16 10:25:23-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:25:23-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-16 10:25:23-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:25:23-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 210.35MB rss 227.21MB
2025-9-16 10:25:23-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (3ms)
2025-9-16 10:25:23-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 3 ms √14%
2025-9-16 10:25:23-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:25:23-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:25:23-debug: options.md5Cache is false.
2025-9-16 10:25:23-debug: Process: heapTotal 231.30MB heapUsed 211.05MB rss 227.74MB
2025-9-16 10:25:23-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-16 10:25:30-debug: refresh db internal success
2025-9-16 10:25:30-debug: asset-db:worker-effect-data-processing (258ms)
2025-9-16 10:25:30-debug: refresh db assets success
2025-9-16 10:25:30-debug: asset-db:refresh-all-database (643ms)
2025-9-16 10:25:39-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene...
2025-9-16 10:25:39-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:25:39-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\test success
2025-9-16 10:25:39-debug: Query all assets info in project
2025-9-16 10:25:39-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 10:25:39-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:25:39-debug:   Number of all scenes: 3
2025-9-16 10:25:39-debug:   Number of other assets: 2719
2025-9-16 10:25:39-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:25:39-debug:   Number of all scripts: 235
2025-9-16 10:25:39-debug: Process: heapTotal 218.59MB heapUsed 200.20MB rss 220.27MB
2025-9-16 10:25:39-debug: 查询 Asset Bundle start0%
2025-9-16 10:25:39-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-16 10:25:39-debug: run build task 查询 Asset Bundle success in 16 ms √3%
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 201.68MB rss 224.24MB
2025-9-16 10:25:39-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:25:39-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:25:39-debug: init bundle assets
2025-9-16 10:25:39-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:25:39-debug: Number of scenes: 2
2025-9-16 10:25:39-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (4ms)
2025-9-16 10:25:39-debug: run build task 查询使用的资源以及资源包配置 success in 4 ms √7%
2025-9-16 10:25:39-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:25:39-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:25:39-debug: sort script group...
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 202.69MB rss 224.38MB
2025-9-16 10:25:39-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 10:25:39-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 10:25:39-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 202.84MB rss 224.34MB
2025-9-16 10:25:39-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:25:39-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-16 10:25:39-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-16 10:25:39-debug: builder.tasks.settings.macro start11%
2025-9-16 10:25:39-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:25:39-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 202.91MB rss 224.22MB
2025-9-16 10:25:39-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-16 10:25:39-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-16 10:25:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:25:39-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 202.95MB rss 224.13MB
2025-9-16 10:25:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-16 10:25:39-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-16 10:25:39-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 203.02MB rss 224.15MB
2025-9-16 10:25:39-debug: custom joint texture layouts start12%
2025-9-16 10:25:39-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 10:25:39-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 10:25:39-debug: // ---- build task custom joint physics ----
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 203.05MB rss 224.15MB
2025-9-16 10:25:39-debug: custom joint physics start12%
2025-9-16 10:25:39-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-16 10:25:39-debug: run build task custom joint physics success in 1 ms √13%
2025-9-16 10:25:39-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 203.08MB rss 224.15MB
2025-9-16 10:25:39-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:25:39-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 10:25:39-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 203.10MB rss 224.15MB
2025-9-16 10:25:39-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:25:39-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-16 10:25:39-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 203.13MB rss 224.15MB
2025-9-16 10:25:39-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:25:39-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:25:39-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-16 10:25:39-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-16 10:25:39-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:25:39-debug: options.md5Cache is false.
2025-9-16 10:25:39-debug: Process: heapTotal 222.59MB heapUsed 201.54MB rss 224.89MB
2025-9-16 10:25:39-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:25:39-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-16 10:25:39-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 10:26:09-debug: refresh db internal success
2025-9-16 10:26:09-debug: asset-db:worker-effect-data-processing (267ms)
2025-9-16 10:26:09-debug: refresh db assets success
2025-9-16 10:26:09-debug: asset-db:refresh-all-database (664ms)
2025-9-16 10:26:13-debug: Query all assets info in project
2025-9-16 10:26:13-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 10:26:13-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:26:13-debug:   Number of all scenes: 3
2025-9-16 10:26:13-debug: 查询 Asset Bundle start0%
2025-9-16 10:26:13-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 200.29MB rss 232.63MB
2025-9-16 10:26:13-debug:   Number of all scripts: 235
2025-9-16 10:26:13-debug:   Number of other assets: 2719
2025-9-16 10:26:13-debug: // ---- build task 查询 Asset Bundle ---- (43ms)
2025-9-16 10:26:13-debug: run build task 查询 Asset Bundle success in 43 ms √3%
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 202.97MB rss 233.57MB
2025-9-16 10:26:13-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:26:13-debug: init bundle assets
2025-9-16 10:26:13-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:26:13-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:26:13-debug: Number of scenes: 2
2025-9-16 10:26:13-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (7ms)
2025-9-16 10:26:13-debug: run build task 查询使用的资源以及资源包配置 success in 7 ms √7%
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 204.03MB rss 232.93MB
2025-9-16 10:26:13-debug: sort script group...
2025-9-16 10:26:13-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:26:13-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:26:13-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 10:26:13-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 10:26:13-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:26:13-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 204.18MB rss 232.89MB
2025-9-16 10:26:13-debug: // ---- build task 初始化 settings.json 与 config.json ---- (2ms)
2025-9-16 10:26:13-debug: run build task 初始化 settings.json 与 config.json success in 2 ms √11%
2025-9-16 10:26:13-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 204.25MB rss 232.89MB
2025-9-16 10:26:13-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:26:13-debug: builder.tasks.settings.macro start11%
2025-9-16 10:26:13-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-16 10:26:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:26:13-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 204.29MB rss 232.81MB
2025-9-16 10:26:13-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-16 10:26:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-16 10:26:13-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms √12%
2025-9-16 10:26:13-debug: custom joint texture layouts start12%
2025-9-16 10:26:13-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 204.36MB rss 232.72MB
2025-9-16 10:26:13-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-16 10:26:13-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-16 10:26:13-debug: custom joint physics start12%
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 204.39MB rss 232.73MB
2025-9-16 10:26:13-debug: // ---- build task custom joint physics ----
2025-9-16 10:26:13-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-16 10:26:13-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:26:13-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:26:13-debug: run build task custom joint physics success in 1 ms √13%
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 204.42MB rss 232.73MB
2025-9-16 10:26:13-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-16 10:26:13-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 204.45MB rss 232.73MB
2025-9-16 10:26:13-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:26:13-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:26:13-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-16 10:26:13-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 204.47MB rss 232.73MB
2025-9-16 10:26:13-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:26:13-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (8ms)
2025-9-16 10:26:13-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 8 ms √14%
2025-9-16 10:26:13-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:26:13-debug: Process: heapTotal 230.14MB heapUsed 205.30MB rss 233.51MB
2025-9-16 10:26:13-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:26:13-debug: options.md5Cache is false.
2025-9-16 10:26:13-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-16 10:26:13-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 10:26:19-debug: refresh db internal success
2025-9-16 10:26:19-debug: asset-db:worker-effect-data-processing (213ms)
2025-9-16 10:26:20-debug: refresh db assets success
2025-9-16 10:26:20-debug: asset-db:refresh-all-database (550ms)
2025-9-16 10:26:20-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene...
2025-9-16 10:26:20-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:26:20-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\test success
2025-9-16 10:26:20-debug: Query all assets info in project
2025-9-16 10:26:21-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 10:26:21-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:26:21-debug:   Number of all scenes: 3
2025-9-16 10:26:21-debug:   Number of all scripts: 235
2025-9-16 10:26:21-debug:   Number of other assets: 2719
2025-9-16 10:26:21-debug: 查询 Asset Bundle start0%
2025-9-16 10:26:21-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:26:21-debug: Process: heapTotal 234.83MB heapUsed 210.08MB rss 237.52MB
2025-9-16 10:26:21-debug: // ---- build task 查询 Asset Bundle ---- (23ms)
2025-9-16 10:26:21-debug: run build task 查询 Asset Bundle success in 23 ms √3%
2025-9-16 10:26:21-debug: Process: heapTotal 234.83MB heapUsed 212.31MB rss 237.54MB
2025-9-16 10:26:21-debug: init bundle assets
2025-9-16 10:26:21-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:26:21-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:26:21-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:26:21-debug: Number of scenes: 2
2025-9-16 10:26:21-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (4ms)
2025-9-16 10:26:21-debug: run build task 查询使用的资源以及资源包配置 success in 4 ms √7%
2025-9-16 10:26:21-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:26:21-debug: sort script group...
2025-9-16 10:26:21-debug: Process: heapTotal 235.08MB heapUsed 207.29MB rss 237.64MB
2025-9-16 10:26:21-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:26:21-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 10:26:21-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:26:21-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 10:26:21-debug: Process: heapTotal 235.08MB heapUsed 207.44MB rss 237.64MB
2025-9-16 10:26:21-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:26:21-debug: // ---- build task 初始化 settings.json 与 config.json ---- (4ms)
2025-9-16 10:26:21-debug: run build task 初始化 settings.json 与 config.json success in 4 ms √11%
2025-9-16 10:26:21-debug: Process: heapTotal 235.08MB heapUsed 207.52MB rss 237.63MB
2025-9-16 10:26:21-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:26:21-debug: builder.tasks.settings.macro start11%
2025-9-16 10:26:21-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:26:21-debug: // ---- build task builder.tasks.settings.macro ---- (2ms)
2025-9-16 10:26:21-debug: run build task builder.tasks.settings.macro success in 2 ms √11%
2025-9-16 10:26:21-debug: Process: heapTotal 235.08MB heapUsed 207.55MB rss 237.42MB
2025-9-16 10:26:21-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:26:21-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:26:21-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-16 10:26:21-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-16 10:26:21-debug: custom joint texture layouts start12%
2025-9-16 10:26:21-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:26:21-debug: Process: heapTotal 235.08MB heapUsed 207.62MB rss 237.45MB
2025-9-16 10:26:21-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 10:26:21-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 10:26:21-debug: Process: heapTotal 235.08MB heapUsed 207.65MB rss 237.45MB
2025-9-16 10:26:21-debug: custom joint physics start12%
2025-9-16 10:26:21-debug: // ---- build task custom joint physics ----
2025-9-16 10:26:21-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-16 10:26:21-debug: run build task custom joint physics success in 1 ms √13%
2025-9-16 10:26:21-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:26:21-debug: Process: heapTotal 235.08MB heapUsed 207.68MB rss 237.45MB
2025-9-16 10:26:21-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:26:21-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 10:26:21-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:26:21-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:26:21-debug: Process: heapTotal 235.08MB heapUsed 207.70MB rss 237.45MB
2025-9-16 10:26:21-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-16 10:26:21-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:26:21-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:26:21-debug: Process: heapTotal 235.08MB heapUsed 207.73MB rss 237.45MB
2025-9-16 10:26:21-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-16 10:26:21-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-16 10:26:21-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:26:21-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:26:21-debug: Process: heapTotal 235.08MB heapUsed 208.42MB rss 237.71MB
2025-9-16 10:26:21-debug: options.md5Cache is false.
2025-9-16 10:26:21-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-16 10:26:21-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 10:26:30-debug: refresh db internal success
2025-9-16 10:26:30-debug: asset-db:worker-effect-data-processing (206ms)
2025-9-16 10:26:30-debug: refresh db assets success
2025-9-16 10:26:30-debug: asset-db:refresh-all-database (552ms)
2025-9-16 10:26:37-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene...
2025-9-16 10:26:37-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:26:37-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\test success
2025-9-16 10:26:37-debug: Query all assets info in project
2025-9-16 10:26:37-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 10:26:37-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:26:37-debug:   Number of all scripts: 235
2025-9-16 10:26:37-debug:   Number of other assets: 2719
2025-9-16 10:26:37-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:26:37-debug: Process: heapTotal 241.80MB heapUsed 221.73MB rss 243.92MB
2025-9-16 10:26:37-debug: 查询 Asset Bundle start0%
2025-9-16 10:26:37-debug:   Number of all scenes: 3
2025-9-16 10:26:37-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-16 10:26:37-debug: run build task 查询 Asset Bundle success in 16 ms √3%
2025-9-16 10:26:37-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 219.37MB rss 244.15MB
2025-9-16 10:26:37-debug: init bundle assets
2025-9-16 10:26:37-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:26:37-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:26:37-debug: Number of scenes: 2
2025-9-16 10:26:37-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (5ms)
2025-9-16 10:26:37-debug: run build task 查询使用的资源以及资源包配置 success in 5 ms √7%
2025-9-16 10:26:37-debug: sort script group...
2025-9-16 10:26:37-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:26:37-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 220.36MB rss 244.09MB
2025-9-16 10:26:37-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:26:37-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-16 10:26:37-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 220.51MB rss 244.09MB
2025-9-16 10:26:37-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:26:37-debug: // ---- build task 初始化 settings.json 与 config.json ---- (10ms)
2025-9-16 10:26:37-debug: run build task 初始化 settings.json 与 config.json success in 10 ms √11%
2025-9-16 10:26:37-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:26:37-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:26:37-debug: builder.tasks.settings.macro start11%
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 220.58MB rss 244.01MB
2025-9-16 10:26:37-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-16 10:26:37-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-16 10:26:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:26:37-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 220.62MB rss 243.91MB
2025-9-16 10:26:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-9-16 10:26:37-debug: run build task 整理部分构建选项内数据到 settings.json success in 4 ms √12%
2025-9-16 10:26:37-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 220.68MB rss 243.93MB
2025-9-16 10:26:37-debug: custom joint texture layouts start12%
2025-9-16 10:26:37-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 10:26:37-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 220.71MB rss 243.93MB
2025-9-16 10:26:37-debug: // ---- build task custom joint physics ----
2025-9-16 10:26:37-debug: custom joint physics start12%
2025-9-16 10:26:37-debug: run build task custom joint physics success in 0 ms √13%
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 220.74MB rss 243.93MB
2025-9-16 10:26:37-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:26:37-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:26:37-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 10:26:37-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:26:37-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 220.76MB rss 243.93MB
2025-9-16 10:26:37-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-16 10:26:37-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:26:37-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 220.79MB rss 243.93MB
2025-9-16 10:26:37-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (3ms)
2025-9-16 10:26:37-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 3 ms √14%
2025-9-16 10:26:37-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:26:37-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:26:37-debug: Process: heapTotal 242.05MB heapUsed 221.48MB rss 243.93MB
2025-9-16 10:26:37-debug: options.md5Cache is false.
2025-9-16 10:26:37-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-16 10:26:37-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 10:26:46-debug: refresh db internal success
2025-9-16 10:26:46-debug: asset-db:worker-effect-data-processing (270ms)
2025-9-16 10:26:46-debug: refresh db assets success
2025-9-16 10:26:46-debug: asset-db:refresh-all-database (629ms)
2025-9-16 10:26:50-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene...
2025-9-16 10:26:50-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:26:50-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\test success
2025-9-16 10:26:51-debug: Query all assets info in project
2025-9-16 10:26:51-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 10:26:51-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:26:51-debug:   Number of other assets: 2719
2025-9-16 10:26:51-debug: 查询 Asset Bundle start0%
2025-9-16 10:26:51-debug:   Number of all scripts: 235
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 204.64MB rss 234.43MB
2025-9-16 10:26:51-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:26:51-debug:   Number of all scenes: 3
2025-9-16 10:26:51-debug: // ---- build task 查询 Asset Bundle ---- (27ms)
2025-9-16 10:26:51-debug: run build task 查询 Asset Bundle success in 27 ms √3%
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 202.53MB rss 234.41MB
2025-9-16 10:26:51-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:26:51-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:26:51-debug: init bundle assets
2025-9-16 10:26:51-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:26:51-debug: Number of scenes: 2
2025-9-16 10:26:51-debug: run build task 查询使用的资源以及资源包配置 success in 9 ms √7%
2025-9-16 10:26:51-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (9ms)
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 203.55MB rss 235.22MB
2025-9-16 10:26:51-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:26:51-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:26:51-debug: sort script group...
2025-9-16 10:26:51-debug: // ---- build task 整理脚本分组与脚本数据 ---- (2ms)
2025-9-16 10:26:51-debug: run build task 整理脚本分组与脚本数据 success in 2 ms √10%
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 203.74MB rss 234.66MB
2025-9-16 10:26:51-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:26:51-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:26:51-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-16 10:26:51-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-16 10:26:51-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:26:51-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 203.82MB rss 234.49MB
2025-9-16 10:26:51-debug: builder.tasks.settings.macro start11%
2025-9-16 10:26:51-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-16 10:26:51-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-16 10:26:51-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:26:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 203.85MB rss 234.36MB
2025-9-16 10:26:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-16 10:26:51-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms √12%
2025-9-16 10:26:51-debug: custom joint texture layouts start12%
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 203.92MB rss 234.31MB
2025-9-16 10:26:51-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:26:51-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 10:26:51-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 10:26:51-debug: custom joint physics start12%
2025-9-16 10:26:51-debug: // ---- build task custom joint physics ----
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 203.95MB rss 234.31MB
2025-9-16 10:26:51-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:26:51-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 203.98MB rss 234.31MB
2025-9-16 10:26:51-debug: run build task custom joint physics success in 0 ms √13%
2025-9-16 10:26:51-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 10:26:51-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 204.00MB rss 234.31MB
2025-9-16 10:26:51-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:26:51-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:26:51-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 204.03MB rss 234.31MB
2025-9-16 10:26:51-debug: run build task 填充场景数据到 settings.json success in 0 ms √14%
2025-9-16 10:26:51-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (6ms)
2025-9-16 10:26:51-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 6 ms √14%
2025-9-16 10:26:51-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:26:51-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:26:51-debug: options.md5Cache is false.
2025-9-16 10:26:51-debug: Process: heapTotal 232.34MB heapUsed 204.83MB rss 235.23MB
2025-9-16 10:26:51-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-16 10:27:03-debug: refresh db internal success
2025-9-16 10:27:03-debug: asset-db:worker-effect-data-processing (295ms)
2025-9-16 10:27:04-debug: refresh db assets success
2025-9-16 10:27:04-debug: asset-db:refresh-all-database (673ms)
2025-9-16 10:27:11-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene...
2025-9-16 10:27:11-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:27:11-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\test success
2025-9-16 10:27:11-debug: Query all assets info in project
2025-9-16 10:27:11-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 10:27:11-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:27:11-debug:   Number of all scripts: 235
2025-9-16 10:27:11-debug:   Number of other assets: 2719
2025-9-16 10:27:11-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 200.82MB rss 233.56MB
2025-9-16 10:27:11-debug: 查询 Asset Bundle start0%
2025-9-16 10:27:11-debug:   Number of all scenes: 3
2025-9-16 10:27:11-debug: // ---- build task 查询 Asset Bundle ---- (14ms)
2025-9-16 10:27:11-debug: run build task 查询 Asset Bundle success in 14 ms √3%
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 203.25MB rss 233.54MB
2025-9-16 10:27:11-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:27:11-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:27:11-debug: init bundle assets
2025-9-16 10:27:11-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:27:11-debug: Number of scenes: 2
2025-9-16 10:27:11-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-16 10:27:11-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-16 10:27:11-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 204.25MB rss 233.52MB
2025-9-16 10:27:11-debug: sort script group...
2025-9-16 10:27:11-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:27:11-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 10:27:11-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 204.39MB rss 233.50MB
2025-9-16 10:27:11-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:27:11-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:27:11-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-16 10:27:11-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-16 10:27:11-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:27:11-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:27:11-debug: builder.tasks.settings.macro start11%
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 204.47MB rss 233.42MB
2025-9-16 10:27:11-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-16 10:27:11-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:27:11-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-16 10:27:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 204.50MB rss 233.32MB
2025-9-16 10:27:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-16 10:27:11-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms √12%
2025-9-16 10:27:11-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 204.58MB rss 233.34MB
2025-9-16 10:27:11-debug: custom joint texture layouts start12%
2025-9-16 10:27:11-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 10:27:11-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 204.61MB rss 233.34MB
2025-9-16 10:27:11-debug: custom joint physics start12%
2025-9-16 10:27:11-debug: // ---- build task custom joint physics ----
2025-9-16 10:27:11-debug: run build task custom joint physics success in 0 ms √13%
2025-9-16 10:27:11-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 204.63MB rss 233.34MB
2025-9-16 10:27:11-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:27:11-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 10:27:11-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:27:11-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 204.66MB rss 233.34MB
2025-9-16 10:27:11-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-16 10:27:11-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-16 10:27:11-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:27:11-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 204.68MB rss 233.34MB
2025-9-16 10:27:11-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 4 ms √14%
2025-9-16 10:27:11-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:27:11-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:27:11-debug: options.md5Cache is false.
2025-9-16 10:27:11-debug: Process: heapTotal 230.64MB heapUsed 199.61MB rss 233.97MB
2025-9-16 10:27:11-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (4ms)
2025-9-16 10:27:11-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-16 10:27:11-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 10:27:16-debug: refresh db internal success
2025-9-16 10:27:17-debug: asset-db:worker-effect-data-processing (102ms)
2025-9-16 10:27:17-debug: refresh db assets success
2025-9-16 10:27:17-debug: asset-db:refresh-all-database (252ms)
2025-9-16 10:27:18-debug: refresh db internal success
2025-9-16 10:27:19-debug: asset-db:worker-effect-data-processing (105ms)
2025-9-16 10:27:19-debug: refresh db assets success
2025-9-16 10:27:19-debug: asset-db:refresh-all-database (257ms)
2025-9-16 10:27:20-debug: refresh db internal success
2025-9-16 10:27:20-debug: asset-db:worker-effect-data-processing (106ms)
2025-9-16 10:27:20-debug: refresh db assets success
2025-9-16 10:27:20-debug: asset-db:refresh-all-database (252ms)
2025-9-16 10:27:24-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene...
2025-9-16 10:27:24-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\test.scene
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:27:24-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\test success
2025-9-16 10:27:25-debug: Query all assets info in project
2025-9-16 10:27:25-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 10:27:25-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 10:27:25-debug:   Number of all scenes: 3
2025-9-16 10:27:25-debug:   Number of all scripts: 235
2025-9-16 10:27:25-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 10:27:25-debug:   Number of other assets: 2719
2025-9-16 10:27:25-debug: Process: heapTotal 236.62MB heapUsed 215.12MB rss 238.34MB
2025-9-16 10:27:25-debug: 查询 Asset Bundle start0%
2025-9-16 10:27:25-debug: run build task 查询 Asset Bundle success in 16 ms √3%
2025-9-16 10:27:25-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 212.78MB rss 238.70MB
2025-9-16 10:27:25-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-9-16 10:27:25-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 10:27:25-debug: init bundle assets
2025-9-16 10:27:25-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 10:27:25-debug: Number of scenes: 2
2025-9-16 10:27:25-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (4ms)
2025-9-16 10:27:25-debug: run build task 查询使用的资源以及资源包配置 success in 4 ms √7%
2025-9-16 10:27:25-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 10:27:25-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 213.76MB rss 238.57MB
2025-9-16 10:27:25-debug: sort script group...
2025-9-16 10:27:25-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 10:27:25-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 10:27:25-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 10:27:25-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 213.91MB rss 238.55MB
2025-9-16 10:27:25-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-16 10:27:25-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-16 10:27:25-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 10:27:25-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 10:27:25-debug: builder.tasks.settings.macro start11%
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 213.98MB rss 238.50MB
2025-9-16 10:27:25-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-16 10:27:25-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 214.01MB rss 238.38MB
2025-9-16 10:27:25-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 10:27:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 10:27:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-16 10:27:25-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms √12%
2025-9-16 10:27:25-debug: custom joint texture layouts start12%
2025-9-16 10:27:25-debug: // ---- build task custom joint texture layouts ----
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 214.08MB rss 238.40MB
2025-9-16 10:27:25-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 10:27:25-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 10:27:25-debug: custom joint physics start12%
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 214.11MB rss 238.40MB
2025-9-16 10:27:25-debug: // ---- build task custom joint physics ----
2025-9-16 10:27:25-debug: // ---- build task custom joint physics ---- (1ms)
2025-9-16 10:27:25-debug: run build task custom joint physics success in 1 ms √13%
2025-9-16 10:27:25-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 10:27:25-debug: 填充脚本数据到 settings.json start13%
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 214.14MB rss 238.40MB
2025-9-16 10:27:25-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 10:27:25-debug: 填充场景数据到 settings.json start13%
2025-9-16 10:27:25-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 214.17MB rss 238.40MB
2025-9-16 10:27:25-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-16 10:27:25-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-16 10:27:25-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 10:27:25-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 214.20MB rss 238.40MB
2025-9-16 10:27:25-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 4 ms √14%
2025-9-16 10:27:25-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (4ms)
2025-9-16 10:27:25-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 10:27:25-debug: Process: heapTotal 236.87MB heapUsed 214.84MB rss 238.40MB
2025-9-16 10:27:25-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 10:27:25-debug: options.md5Cache is false.
2025-9-16 10:27:25-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 0 ms √15%
2025-9-16 10:35:18-debug: refresh db internal success
2025-9-16 10:35:19-debug: asset-db:worker-effect-data-processing (291ms)
2025-9-16 10:35:19-debug: refresh db assets success
2025-9-16 10:35:19-debug: asset-db:refresh-all-database (690ms)
2025-9-16 10:35:43-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng...
2025-9-16 10:35:43-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:35:43-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect success
2025-9-16 10:35:44-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:35:55-debug: start move asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.atlas -> F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.atlas...
2025-9-16 10:35:55-debug: start move asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.json -> F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.json...
2025-9-16 10:35:55-debug: start move asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.png -> F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.png...
2025-9-16 10:35:55-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.json...
2025-9-16 10:35:55-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.png...
2025-9-16 10:35:55-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.atlas...
2025-9-16 10:35:55-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.json
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:35:55-log: UUID is initialized for "%s".
F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.png
2025-9-16 10:35:55-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.png
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:35:55-debug: %cReImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:35:55-debug: %cReImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:35:55-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.atlas
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:35:55-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng success
2025-9-16 10:35:55-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng success
2025-9-16 10:35:55-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test...
2025-9-16 10:35:55-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng success
2025-9-16 10:35:55-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test...
2025-9-16 10:35:55-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test...
2025-9-16 10:35:55-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\test
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:35:55-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.json
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:35:55-log: UUID is initialized for "%s".
F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.png
2025-9-16 10:35:55-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets success
2025-9-16 10:35:55-debug: move asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.json -> F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.json success
2025-9-16 10:35:55-debug: move asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.png -> F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.png success
2025-9-16 10:35:55-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets success
2025-9-16 10:35:55-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets success
2025-9-16 10:35:55-debug: move asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\test\hecheng.atlas -> F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng\hecheng.atlas success
2025-9-16 10:35:55-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\danlu_hecheng
background: #aaff85; color: #000;
color: #000;
2025-9-16 10:41:05-debug: refresh db internal success
2025-9-16 10:41:05-debug: asset-db:worker-effect-data-processing (164ms)
2025-9-16 10:41:05-debug: refresh db assets success
2025-9-16 10:41:05-debug: asset-db:refresh-all-database (348ms)
2025-9-16 11:30:34-debug: refresh db internal success
2025-9-16 11:30:35-debug: asset-db:worker-effect-data-processing (532ms)
2025-9-16 11:30:35-debug: refresh db assets success
2025-9-16 11:30:35-debug: asset-db:refresh-all-database (1052ms)
2025-9-16 11:32:48-debug: refresh db internal success
2025-9-16 11:32:48-debug: asset-db:worker-effect-data-processing (108ms)
2025-9-16 11:32:48-debug: refresh db assets success
2025-9-16 11:32:48-debug: asset-db:refresh-all-database (261ms)
2025-9-16 11:32:53-debug: refresh db internal success
2025-9-16 11:32:53-debug: asset-db:worker-effect-data-processing (134ms)
2025-9-16 11:32:53-debug: refresh db assets success
2025-9-16 11:32:53-debug: asset-db:refresh-all-database (283ms)
2025-9-16 11:33:47-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-16 11:33:47-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:33:47-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-16 11:33:56-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-16 11:33:56-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:33:56-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-16 11:34:00-debug: Query all assets info in project
2025-9-16 11:34:00-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 11:34:00-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 11:34:00-debug:   Number of all scripts: 235
2025-9-16 11:34:00-debug: 查询 Asset Bundle start0%
2025-9-16 11:34:00-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 11:34:00-debug: Process: heapTotal 225.86MB heapUsed 208.25MB rss 228.88MB
2025-9-16 11:34:00-debug:   Number of other assets: 2719
2025-9-16 11:34:00-debug:   Number of all scenes: 3
2025-9-16 11:34:00-debug: // ---- build task 查询 Asset Bundle ---- (14ms)
2025-9-16 11:34:00-debug: run build task 查询 Asset Bundle success in 14 ms √3%
2025-9-16 11:34:00-debug: init bundle assets
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 210.95MB rss 229.98MB
2025-9-16 11:34:00-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 11:34:00-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 11:34:00-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 11:34:00-debug: Number of scenes: 2
2025-9-16 11:34:00-debug: run build task 查询使用的资源以及资源包配置 success in 11 ms √7%
2025-9-16 11:34:00-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (11ms)
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 211.96MB rss 231.05MB
2025-9-16 11:34:00-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 11:34:00-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 11:34:00-debug: sort script group...
2025-9-16 11:34:00-debug: // ---- build task 整理脚本分组与脚本数据 ---- (1ms)
2025-9-16 11:34:00-debug: run build task 整理脚本分组与脚本数据 success in 1 ms √10%
2025-9-16 11:34:00-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 212.11MB rss 231.14MB
2025-9-16 11:34:00-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 11:34:00-debug: // ---- build task 初始化 settings.json 与 config.json ---- (1ms)
2025-9-16 11:34:00-debug: run build task 初始化 settings.json 与 config.json success in 1 ms √11%
2025-9-16 11:34:00-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 212.18MB rss 231.07MB
2025-9-16 11:34:00-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 11:34:00-debug: builder.tasks.settings.macro start11%
2025-9-16 11:34:00-debug: // ---- build task builder.tasks.settings.macro ---- (1ms)
2025-9-16 11:34:00-debug: run build task builder.tasks.settings.macro success in 1 ms √11%
2025-9-16 11:34:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 212.22MB rss 231.02MB
2025-9-16 11:34:00-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 11:34:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-9-16 11:34:00-debug: run build task 整理部分构建选项内数据到 settings.json success in 7 ms √12%
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 212.29MB rss 231.11MB
2025-9-16 11:34:00-debug: custom joint texture layouts start12%
2025-9-16 11:34:00-debug: // ---- build task custom joint texture layouts ----
2025-9-16 11:34:00-debug: // ---- build task custom joint texture layouts ---- (1ms)
2025-9-16 11:34:00-debug: run build task custom joint texture layouts success in 1 ms √12%
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 212.32MB rss 231.17MB
2025-9-16 11:34:00-debug: // ---- build task custom joint physics ----
2025-9-16 11:34:00-debug: custom joint physics start12%
2025-9-16 11:34:00-debug: run build task custom joint physics success in 0 ms √13%
2025-9-16 11:34:00-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 11:34:00-debug: 填充脚本数据到 settings.json start13%
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 212.34MB rss 231.17MB
2025-9-16 11:34:00-debug: run build task 填充脚本数据到 settings.json success in 0 ms √13%
2025-9-16 11:34:00-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 11:34:00-debug: 填充场景数据到 settings.json start13%
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 212.37MB rss 231.17MB
2025-9-16 11:34:00-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-16 11:34:00-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 11:34:00-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 212.39MB rss 231.19MB
2025-9-16 11:34:00-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-16 11:34:00-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (7ms)
2025-9-16 11:34:00-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 7 ms √14%
2025-9-16 11:34:00-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 11:34:00-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 11:34:00-debug: Process: heapTotal 233.86MB heapUsed 213.26MB rss 232.90MB
2025-9-16 11:34:00-debug: options.md5Cache is false.
2025-9-16 11:34:00-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-16 11:34:00-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 11:36:37-debug: refresh db internal success
2025-9-16 11:36:37-debug: asset-db:worker-effect-data-processing (249ms)
2025-9-16 11:36:37-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\scripts\ui\elixir\ElixirView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:36:37-debug: refresh db assets success
2025-9-16 11:36:37-debug: asset-db:refresh-all-database (606ms)
2025-9-16 11:36:39-debug: Query all assets info in project
2025-9-16 11:36:40-debug: BuildAssetLibrary query-assets with assets 3102
2025-9-16 11:36:40-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-16 11:36:40-debug:   Number of all scenes: 3
2025-9-16 11:36:40-debug:   Number of other assets: 2719
2025-9-16 11:36:40-debug:   Number of all scripts: 235
2025-9-16 11:36:40-debug: // ---- build task 查询 Asset Bundle ----
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 201.78MB rss 233.41MB
2025-9-16 11:36:40-debug: 查询 Asset Bundle start0%
2025-9-16 11:36:40-debug: // ---- build task 查询 Asset Bundle ---- (52ms)
2025-9-16 11:36:40-debug: run build task 查询 Asset Bundle success in 52 ms √3%
2025-9-16 11:36:40-debug: 查询使用的资源以及资源包配置 start3%
2025-9-16 11:36:40-debug: // ---- build task 查询使用的资源以及资源包配置 ----
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 204.45MB rss 233.41MB
2025-9-16 11:36:40-debug: init bundle assets
2025-9-16 11:36:40-debug: Query preload assets from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,ff9be190-20a4-4e48-b68c-76e3c7cff085,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,9361fd90-ba52-4f84-aa93-6e878fd576ca,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98
2025-9-16 11:36:40-debug: Number of scenes: 2
2025-9-16 11:36:40-debug: // ---- build task 查询使用的资源以及资源包配置 ---- (6ms)
2025-9-16 11:36:40-debug: run build task 查询使用的资源以及资源包配置 success in 6 ms √7%
2025-9-16 11:36:40-debug: 整理脚本分组与脚本数据 start7%
2025-9-16 11:36:40-debug: // ---- build task 整理脚本分组与脚本数据 ----
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 205.47MB rss 233.38MB
2025-9-16 11:36:40-debug: sort script group...
2025-9-16 11:36:40-debug: // ---- build task 整理脚本分组与脚本数据 ---- (5ms)
2025-9-16 11:36:40-debug: run build task 整理脚本分组与脚本数据 success in 5 ms √10%
2025-9-16 11:36:40-debug: 初始化 settings.json 与 config.json start10%
2025-9-16 11:36:40-debug: // ---- build task 初始化 settings.json 与 config.json ----
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 205.62MB rss 233.36MB
2025-9-16 11:36:40-debug: // ---- build task 初始化 settings.json 与 config.json ---- (9ms)
2025-9-16 11:36:40-debug: run build task 初始化 settings.json 与 config.json success in 9 ms √11%
2025-9-16 11:36:40-debug: builder.tasks.settings.macro 字段未在编辑器多语言内定义
2025-9-16 11:36:40-debug: builder.tasks.settings.macro start11%
2025-9-16 11:36:40-debug: // ---- build task builder.tasks.settings.macro ----
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 205.69MB rss 233.28MB
2025-9-16 11:36:40-debug: // ---- build task builder.tasks.settings.macro ---- (4ms)
2025-9-16 11:36:40-debug: run build task builder.tasks.settings.macro success in 4 ms √11%
2025-9-16 11:36:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-16 11:36:40-debug: 整理部分构建选项内数据到 settings.json start11%
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 205.73MB rss 233.18MB
2025-9-16 11:36:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (11ms)
2025-9-16 11:36:40-debug: run build task 整理部分构建选项内数据到 settings.json success in 11 ms √12%
2025-9-16 11:36:40-debug: // ---- build task custom joint texture layouts ----
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 205.80MB rss 233.20MB
2025-9-16 11:36:40-debug: custom joint texture layouts start12%
2025-9-16 11:36:40-debug: // ---- build task custom joint texture layouts ---- (2ms)
2025-9-16 11:36:40-debug: run build task custom joint texture layouts success in 2 ms √12%
2025-9-16 11:36:40-debug: custom joint physics start12%
2025-9-16 11:36:40-debug: // ---- build task custom joint physics ----
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 205.83MB rss 233.21MB
2025-9-16 11:36:40-debug: run build task custom joint physics success in 0 ms √13%
2025-9-16 11:36:40-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-16 11:36:40-debug: 填充脚本数据到 settings.json start13%
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 205.86MB rss 233.21MB
2025-9-16 11:36:40-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-16 11:36:40-debug: run build task 填充脚本数据到 settings.json success in 1 ms √13%
2025-9-16 11:36:40-debug: 填充场景数据到 settings.json start13%
2025-9-16 11:36:40-debug: // ---- build task 填充场景数据到 settings.json ----
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 205.88MB rss 233.21MB
2025-9-16 11:36:40-debug: // ---- build task 填充场景数据到 settings.json ---- (1ms)
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 205.91MB rss 233.21MB
2025-9-16 11:36:40-debug: 整理资源分组，生成 packedAssets 以及 rawAssets 数据 start14%
2025-9-16 11:36:40-debug: run build task 填充场景数据到 settings.json success in 1 ms √14%
2025-9-16 11:36:40-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ----
2025-9-16 11:36:40-debug: // ---- build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 ---- (11ms)
2025-9-16 11:36:40-debug: run build task 整理资源分组，生成 packedAssets 以及 rawAssets 数据 success in 11 ms √14%
2025-9-16 11:36:40-debug: 从 suffixMap 生成 md5AssetsMap 到 settings.json start14%
2025-9-16 11:36:40-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ----
2025-9-16 11:36:40-debug: Process: heapTotal 231.05MB heapUsed 206.74MB rss 233.93MB
2025-9-16 11:36:40-debug: options.md5Cache is false.
2025-9-16 11:36:40-debug: // ---- build task 从 suffixMap 生成 md5AssetsMap 到 settings.json ---- (1ms)
2025-9-16 11:36:40-debug: run build task 从 suffixMap 生成 md5AssetsMap 到 settings.json success in 1 ms √15%
2025-9-16 11:36:45-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab...
2025-9-16 11:36:45-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir\ElixirView.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:36:45-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\game\ui\elixir success
2025-9-16 11:36:49-debug: refresh db internal success
2025-9-16 11:36:50-debug: asset-db:worker-effect-data-processing (139ms)
2025-9-16 11:36:50-debug: refresh db assets success
2025-9-16 11:36:50-debug: asset-db:refresh-all-database (319ms)
2025-9-16 11:37:49-debug: refresh db internal success
2025-9-16 11:37:49-debug: asset-db:worker-effect-data-processing (165ms)
2025-9-16 11:37:49-debug: refresh db assets success
2025-9-16 11:37:49-debug: asset-db:refresh-all-database (397ms)
2025-9-16 11:38:07-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng...
2025-9-16 11:38:07-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:38:07-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect success
2025-9-16 11:38:07-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:38:13-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng\skeleton.png...
2025-9-16 11:38:13-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng\skeleton.png
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:38:13-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng\skeleton.atlas...
2025-9-16 11:38:13-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng\skeleton.json...
2025-9-16 11:38:13-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng\skeleton.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:38:13-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng\skeleton.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:38:13-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng\skeleton.atlas
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:38:13-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng\skeleton.json
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:38:13-debug: refresh db internal success
2025-9-16 11:38:13-log: UUID is initialized for "%s".
F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng\skeleton.png
2025-9-16 11:38:13-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng success
2025-9-16 11:38:13-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng success
2025-9-16 11:38:13-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng success
2025-9-16 11:38:14-debug: asset-db:worker-effect-data-processing (142ms)
2025-9-16 11:38:14-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_hecheng
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:38:14-debug: refresh db assets success
2025-9-16 11:38:14-debug: asset-db:refresh-all-database (326ms)
2025-9-16 11:39:19-debug: refresh db internal success
2025-9-16 11:39:19-debug: asset-db:worker-effect-data-processing (146ms)
2025-9-16 11:39:19-debug: refresh db assets success
2025-9-16 11:39:19-debug: asset-db:refresh-all-database (301ms)
2025-9-16 11:39:27-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click...
2025-9-16 11:39:27-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:27-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect success
2025-9-16 11:39:27-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:36-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift...
2025-9-16 11:39:36-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:36-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect success
2025-9-16 11:39:36-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:43-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.png...
2025-9-16 11:39:43-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.png
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:43-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.json...
2025-9-16 11:39:43-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.atlas...
2025-9-16 11:39:43-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:43-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:43-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.json
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:43-log: UUID is initialized for "%s".
F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.png
2025-9-16 11:39:43-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.atlas
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:43-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click success
2025-9-16 11:39:43-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click success
2025-9-16 11:39:43-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click success
2025-9-16 11:39:43-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.json
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:43-log: UUID is initialized for "%s".
F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click\huode.png
2025-9-16 11:39:43-debug: refresh db internal success
2025-9-16 11:39:43-debug: asset-db:worker-effect-data-processing (139ms)
2025-9-16 11:39:43-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_click
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:43-debug: refresh db assets success
2025-9-16 11:39:43-debug: asset-db:refresh-all-database (310ms)
2025-9-16 11:39:53-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.png...
2025-9-16 11:39:53-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.png
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:53-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.json...
2025-9-16 11:39:53-debug: start refresh asset from F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.atlas...
2025-9-16 11:39:53-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:53-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:53-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.json
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:53-log: UUID is initialized for "%s".
F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.png
2025-9-16 11:39:53-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.atlas
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:53-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift success
2025-9-16 11:39:53-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift success
2025-9-16 11:39:53-debug: refresh asset F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift success
2025-9-16 11:39:53-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.json
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:53-log: UUID is initialized for "%s".
F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift\liwugaibian.png
2025-9-16 11:39:53-debug: refresh db internal success
2025-9-16 11:39:53-debug: %cImport%c: F:\SVN\plinko\BasePro\DemoMoveV3\assets\games\animation\effect\xiandan_gift
background: #aaff85; color: #000;
color: #000;
2025-9-16 11:39:53-debug: asset-db:worker-effect-data-processing (144ms)
2025-9-16 11:39:53-debug: refresh db assets success
2025-9-16 11:39:53-debug: asset-db:refresh-all-database (304ms)
2025-9-16 11:40:17-debug: refresh db internal success
2025-9-16 11:40:17-debug: asset-db:worker-effect-data-processing (164ms)
2025-9-16 11:40:17-debug: refresh db assets success
2025-9-16 11:40:17-debug: asset-db:refresh-all-database (330ms)
2025-9-16 11:41:00-debug: refresh db internal success
2025-9-16 11:41:01-debug: asset-db:worker-effect-data-processing (143ms)
2025-9-16 11:41:01-debug: refresh db assets success
2025-9-16 11:41:01-debug: asset-db:refresh-all-database (307ms)
2025-9-16 11:41:43-debug: refresh db internal success
2025-9-16 11:41:43-debug: asset-db:worker-effect-data-processing (145ms)
2025-9-16 11:41:44-debug: refresh db assets success
2025-9-16 11:41:44-debug: asset-db:refresh-all-database (303ms)
2025-9-16 11:41:45-debug: refresh db internal success
2025-9-16 11:41:45-debug: asset-db:worker-effect-data-processing (114ms)
2025-9-16 11:41:45-debug: refresh db assets success
2025-9-16 11:41:45-debug: asset-db:refresh-all-database (269ms)
