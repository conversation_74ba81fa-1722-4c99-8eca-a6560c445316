System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, oops, UIID, Message, tips, elixirUnsealCfg, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, ElixirNode;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../core/Oop", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIID(extras) {
    _reporterNs.report("UIID", "../../common/GameUIConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMessage(extras) {
    _reporterNs.report("Message", "../../event/MessageManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDigTreasure(extras) {
    _reporterNs.report("DigTreasure", "../../proto/DigTreasureProtocol_client", _context.meta, extras);
  }

  function _reportPossibleCrUseOftips(extras) {
    _reporterNs.report("tips", "../../manager/TipsManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfelixirUnsealCfg(extras) {
    _reporterNs.report("elixirUnsealCfg", "../../game/model/ElixirUnsealCfg", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      UIID = _unresolved_3.UIID;
    }, function (_unresolved_4) {
      Message = _unresolved_4.Message;
    }, function (_unresolved_5) {
      tips = _unresolved_5.tips;
    }, function (_unresolved_6) {
      elixirUnsealCfg = _unresolved_6.elixirUnsealCfg;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "791b1NSpexFHIunKlEP/5Ol", "ElixirNode", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("ElixirNode", ElixirNode = (_dec = ccclass('ElixirNode'), _dec2 = property(Label), _dec3 = property(Label), _dec4 = property([Label]), _dec(_class = (_class2 = class ElixirNode extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "labOrder", _descriptor, this);

          _initializerDefineProperty(this, "labTimer", _descriptor2, this);

          _initializerDefineProperty(this, "labNearlyList", _descriptor3, this);

          this.onceBeginData = null;
          this.autoBeginData = null;
        }

        onLoad() {
          (_crd && elixirUnsealCfg === void 0 ? (_reportPossibleCrUseOfelixirUnsealCfg({
            error: Error()
          }), elixirUnsealCfg) : elixirUnsealCfg).init();
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).on("on_elixir_auto_kaiding_begin", this.onKaidingBegin, this);
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).on("on_elixir_auto_kaiding_finish", this.onKaidingFinish, this);
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).on("on_elixir_auto_kaiding_abort", this.onKaidingAbort, this);
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).on("elixir_kaiding_mingxi_result", this.onKaidingMingXiResult, this);
        }

        update(dt) {
          this.updateLabel();
          const kaidingData = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getNextAutoKaiDingInfo((_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getEMapType());

          if (kaidingData && !(_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.countDownCheck) {
            const curTime = Date.now() + (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).game.getServerTimeOffset();
            const timeData = (kaidingData.nextKaiDingTime.toNumber() * 1000 - curTime) / 1000;

            if (timeData < 6) {// if (oops.gui.has(UIID.TakeBackView)) {
              //     const nodeTake = oops.gui.getNode(UIID.TakeBackView);
              //     const takeBackView = nodeTake.getComponent(TakeBackView) as TakeBackView;
              //     if (takeBackView && !takeBackView.getRecaliamState()) {
              //         return
              //     }
              // }
              // this.showCountDownNode(reclaimData.nextBaoWuAutoReclaimTime.toNumber());
            }
          }
        }

        onBtnManulElixir() {
          console.debug('onBtnManulElixir');

          if (!(_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.has((_crd && UIID === void 0 ? (_reportPossibleCrUseOfUIID({
            error: Error()
          }), UIID) : UIID).ElixirView)) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.open((_crd && UIID === void 0 ? (_reportPossibleCrUseOfUIID({
              error: Error()
            }), UIID) : UIID).ElixirView);
          }
        }

        updateLabel() {
          let kaidingData = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getNextAutoKaiDingInfo((_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getEMapType());

          if (kaidingData) {
            const batId = kaidingData.nextBatId;
            this.labOrder.string = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).game.getLastDashSurroundingNumbers(batId);
            const curTime = Date.now() + (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).game.getServerTimeOffset();
            const timeData = Math.floor((kaidingData.nextKaiDingTime.toNumber() * 1000 - curTime) / 1000);
            const minutes = Math.floor(timeData / 60);
            const remainingSeconds = timeData % 60;
            const formattedMinutes = minutes.toString().length < 2 ? `0${minutes.toString()}` : minutes.toString();
            const formattedSeconds = remainingSeconds.toString().length < 2 ? `0${remainingSeconds.toString()}` : remainingSeconds.toString();
            this.labTimer.string = minutes <= 0 && remainingSeconds <= 0 ? "00:00" : `${formattedMinutes}:${formattedSeconds}`;
          }
        }

        onKaidingBegin(event, data) {
          data.bool = true;
          this.autoBeginData = data;
        }

        onKaidingFinish() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.m_gameNet.Send_ReqSelfKaiDingMingXi((_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getEMapType());
        }

        onKaidingAbort(event, batID) {
          if ((_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.has((_crd && UIID === void 0 ? (_reportPossibleCrUseOfUIID({
            error: Error()
          }), UIID) : UIID).CoutDownNode)) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.remove((_crd && UIID === void 0 ? (_reportPossibleCrUseOfUIID({
              error: Error()
            }), UIID) : UIID).CoutDownNode);
          }

          let batIdStr = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getLastDashSurroundingNumbers(batID);
          let content = `【${batIdStr}】本次仙丹开鼎异常，请等待下次开鼎`; //弹窗提示

          (_crd && tips === void 0 ? (_reportPossibleCrUseOftips({
            error: Error()
          }), tips) : tips).showTipsPop(content, null, null, false, "提示");
          this.updateLabel();
        }

        onKaidingMingXiResult() {
          const eMapType = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getEMapType();
          const mingxiListData = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.getKaiDingMingXiListData(eMapType);

          if (mingxiListData) {
            const mxData = mingxiListData[mingxiListData.length - 1];

            if (mxData && mxData.opMingXi) {
              const opMingXi = JSON.parse(mxData.opMingXi);

              if (opMingXi.weiChuCounts.length) {
                for (let i = 0; i < opMingXi.weiChuCounts.length; i++) {
                  let jushu = opMingXi.weiChuCounts[i];
                  this.labNearlyList[i].string = jushu >= 2 ? `${jushu}局未出` : "最近出现";
                }
              }
            }
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "labOrder", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "labTimer", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "labNearlyList", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=a3d1a9ac579311707a4de00bd66adf1dd4452e93.js.map