import { _decorator, Component, Label, Node } from 'cc';
import { oops } from '../../core/Oop';
import { UIID } from '../../common/GameUIConfig';
import { Message } from '../../event/MessageManager';
import { DigTreasure } from '../../proto/DigTreasureProtocol_client';
import { tips } from '../../manager/TipsManager';
import { elixirUnsealCfg } from '../../game/model/ElixirUnsealCfg';
const { ccclass, property } = _decorator;

@ccclass('ElixirNode')
export class ElixirNode extends Component {

    @property(Label)
    private labOrder: Label = null;

    @property(Label)
    private labTimer: Label = null;

    @property([Label])
    private labNearlyList: Label[] = [];

    private onceBeginData = null;
    private autoBeginData = null;

    onLoad() {
        elixirUnsealCfg.init();

        Message.on("on_elixir_auto_kaiding_begin", this.onKaidingBegin, this);
        Message.on("on_elixir_auto_kaiding_finish", this.onKaidingFinish, this);
        Message.on("on_elixir_auto_kaiding_abort", this.onKaidingAbort, this);
        Message.on("elixir_kaiding_mingxi_result", this.onKaidingMingXiResult, this);
    }

    update(dt: number) {
        this.updateLabel();

        const kaidingData = oops.game.getNextAutoKaiDingInfo(oops.game.getEMapType());
        if (kaidingData && !oops.game.countDownCheck) {
            const curTime = Date.now() + oops.game.getServerTimeOffset();
            const timeData = (kaidingData.nextKaiDingTime.toNumber() * 1000 - curTime) / 1000;
            if (timeData < 6) {
                // if (oops.gui.has(UIID.TakeBackView)) {
                //     const nodeTake = oops.gui.getNode(UIID.TakeBackView);
                //     const takeBackView = nodeTake.getComponent(TakeBackView) as TakeBackView;
                //     if (takeBackView && !takeBackView.getRecaliamState()) {
                //         return
                //     }
                // }
                // this.showCountDownNode(reclaimData.nextBaoWuAutoReclaimTime.toNumber());
            }
        }
    }

    protected onBtnManulElixir() {
        console.debug('onBtnManulElixir');
        if (!oops.gui.has(UIID.ElixirView)) {
            oops.gui.open(UIID.ElixirView);
        }
    }

    private updateLabel() {
        let kaidingData = oops.game.getNextAutoKaiDingInfo(oops.game.getEMapType());
        if (kaidingData) {
            const batId = kaidingData.nextBatId;
            this.labOrder.string = oops.game.getLastDashSurroundingNumbers(batId);

            const curTime = Date.now() + oops.game.getServerTimeOffset();
            const timeData = Math.floor((kaidingData.nextKaiDingTime.toNumber() * 1000 - curTime) / 1000);
            const minutes: number = Math.floor(timeData / 60);
            const remainingSeconds: number = timeData % 60;
            const formattedMinutes: string = minutes.toString().length < 2 ? `0${minutes.toString()}` : minutes.toString();
            const formattedSeconds: string = remainingSeconds.toString().length < 2 ? `0${remainingSeconds.toString()}` : remainingSeconds.toString();
            this.labTimer.string = minutes <= 0 && remainingSeconds <= 0 ? "00:00" : `${formattedMinutes}:${formattedSeconds}`;
        }
    }

    private onKaidingBegin(event, data: { batId: string, reclaimXianDanList: DigTreasure.PtGiftAList, bool?: boolean }) {
        data.bool = true;
        this.autoBeginData = data;
    }

    private onKaidingFinish() {
        oops.game.m_gameNet.Send_ReqSelfKaiDingMingXi(oops.game.getEMapType());
    }

    private onKaidingAbort(event, batID: string) {
        if (oops.gui.has(UIID.CoutDownNode)) {
            oops.gui.remove(UIID.CoutDownNode);
        }
        let batIdStr = oops.game.getLastDashSurroundingNumbers(batID);
        let content = `【${batIdStr}】本次仙丹开鼎异常，请等待下次开鼎`;
        //弹窗提示
        tips.showTipsPop(content, null, null, false, "提示")
        this.updateLabel();
    }

    private onKaidingMingXiResult() {
        const eMapType = oops.game.getEMapType();
        const mingxiListData = oops.game.getKaiDingMingXiListData(eMapType);
        if (mingxiListData) {
            const mxData = mingxiListData[mingxiListData.length - 1];
            if (mxData && mxData.opMingXi) {
                const opMingXi = JSON.parse(mxData.opMingXi);
                if (opMingXi.weiChuCounts.length) {
                    for (let i = 0; i < opMingXi.weiChuCounts.length; i++) {
                        let jushu = opMingXi.weiChuCounts[i];
                        this.labNearlyList[i].string = jushu >= 2 ? `${jushu}局未出` : "最近出现";
                    }
                }
            }
        }
    }

}


