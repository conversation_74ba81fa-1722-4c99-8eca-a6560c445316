import { JsonUtil } from "../../util/JsonUtil";

export interface ElixirUnsealType {
    map: number,
    draw: number,
    gift: number,
    name: string,
    value: number,
    count: number,
}

/** 二次解析封装（策划Excel导出的Json静态数据） */
export class ElixirUnsealCfg {
    static TableName: string = "elixir_unseal";

    /** 静态表中一条数据 */
    private data: Map<number, Map<number, ElixirUnsealType[]>>;

    init() {
        if (!!this.data) return this.data;
        this.data = new Map();
        var table = JsonUtil.get(ElixirUnsealCfg.TableName);
        let map1 = new Map<number, ElixirUnsealType[]>();
        let map2 = new Map<number, ElixirUnsealType[]>();
        let map3 = new Map<number, ElixirUnsealType[]>();
        for (let index = 1; index <= Object.keys(table).length; index++) {
            const ele = table[index];
            if (ele.map == 100) {
                const drawList = map1.get(ele.draw) || [];
                ele.map = 4;
                drawList.push(ele);
                map1.set(ele.draw, drawList);
            } else if (ele.map == 1000) {
                const drawList = map2.get(ele.draw) || [];
                ele.map = 5;
                drawList.push(ele);
                map2.set(ele.draw, drawList);
            } else if (ele.map == 10000) {
                const drawList = map3.get(ele.draw) || [];
                ele.map = 6;
                drawList.push(ele);
                map3.set(ele.draw, drawList);
            }
        }
        this.data.set(4, map1);
        this.data.set(5, map2);
        this.data.set(6, map3);
    }

    getData(map: number, draw: number): ElixirUnsealType[] {
        return this.data.get(map).get(draw);
    }

    getDataByMap(map: number): Map<number, ElixirUnsealType[]> {
        return this.data.get(map);
    }

}

export const elixirUnsealCfg = new ElixirUnsealCfg();
