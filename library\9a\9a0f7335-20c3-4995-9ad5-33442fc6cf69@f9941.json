{"__type__": "cc.SpriteFrame", "content": {"name": "hecheng", "atlas": "", "rect": {"x": 2, "y": 2, "width": 482, "height": 119}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 486, "height": 123}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-241, -59.5, 0, 241, -59.5, 0, -241, 59.5, 0, 241, 59.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [2, 121, 484, 121, 2, 2, 484, 2], "nuv": [0.00411522633744856, 0.016260162601626018, 0.9958847736625515, 0.016260162601626018, 0.00411522633744856, 0.983739837398374, 0.9958847736625515, 0.983739837398374], "minPos": {"x": -241, "y": -59.5, "z": 0}, "maxPos": {"x": 241, "y": 59.5, "z": 0}}, "texture": "9a0f7335-20c3-4995-9ad5-33442fc6cf69@6c48a", "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}}