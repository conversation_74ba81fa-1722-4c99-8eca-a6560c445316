{"__type__": "sp.SkeletonData", "_name": "li<PERSON><PERSON><PERSON>", "_objFlags": 0, "_native": "", "_skeletonJson": {"skeleton": {"hash": "dd8T+ppXYgy8Rdw+iNtVKPuiRj0", "spine": "3.8.99", "x": -116, "y": -116, "width": 232, "height": 232, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "all", "parent": "root"}, {"name": "all_tx", "parent": "all"}, {"name": "guang2", "parent": "all_tx"}, {"name": "gq1", "parent": "all_tx", "scaleX": 0.63, "scaleY": 0.63, "color": "ff0000ff"}, {"name": "gq2", "parent": "all_tx", "scaleX": 0.63, "scaleY": 0.63, "color": "ff0000ff"}, {"name": "dian", "parent": "all_tx"}, {"name": "dian2", "parent": "all_tx"}, {"name": "<PERSON><PERSON>", "parent": "all_tx"}, {"name": "dian3", "parent": "all_tx", "rotation": -55.58}, {"name": "ab", "parent": "all"}, {"name": "gq", "parent": "all", "color": "ff0000ff"}, {"name": "xz", "parent": "all", "scaleX": 0.7514, "scaleY": 0.7514}, {"name": "xz2", "parent": "all"}, {"name": "xuanzhan", "parent": "all_tx", "rotation": -4.43}, {"name": "star", "parent": "all_tx"}, {"name": "bao6", "parent": "all", "rotation": 90, "color": "0000ffff"}], "slots": [{"name": "xuanzhan", "bone": "xuanzhan", "blend": "additive"}, {"name": "gq1", "bone": "gq1", "color": "ffb91aff", "blend": "additive"}, {"name": "gq2", "bone": "gq2", "color": "ffb91aff", "blend": "additive"}, {"name": "dian", "bone": "dian", "color": "ffb91aff", "blend": "additive"}, {"name": "dian3", "bone": "dian3", "color": "ffb91aff", "blend": "additive"}, {"name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>", "color": "ffb91aff", "blend": "additive"}, {"name": "dian2", "bone": "dian2", "color": "ffb91aff", "blend": "additive"}, {"name": "star", "bone": "star", "color": "ffb91aff", "attachment": "star_datu", "blend": "additive"}, {"name": "guang2", "bone": "guang2", "attachment": "guang2", "blend": "additive"}, {"name": "ab", "bone": "ab", "attachment": "ab", "blend": "additive"}, {"name": "gq", "bone": "gq", "blend": "additive"}, {"name": "xz3", "bone": "xz", "attachment": "alizi", "blend": "additive"}, {"name": "xz2", "bone": "xz2", "attachment": "as", "blend": "additive"}, {"name": "xz5", "bone": "xz2", "attachment": "as", "blend": "additive"}, {"name": "xz4", "bone": "xz", "attachment": "xz3", "blend": "additive"}, {"name": "bao6", "bone": "bao6", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"ab": {"ab": {"width": 80, "height": 80}}, "adian": {"adian": {"width": 222, "height": 214}}, "bao6": {"bao6": {"rotation": 90, "width": 128, "height": 128}}, "dian": {"dian": {"width": 232, "height": 232}}, "dian2": {"dian": {"width": 232, "height": 232}}, "dian3": {"dian": {"width": 232, "height": 232}}, "gq": {"gq3": {"width": 232, "height": 232}}, "gq1": {"gq1": {"width": 232, "height": 232}}, "gq2": {"gq1": {"width": 232, "height": 232}}, "guang2": {"guang2": {"width": 232, "height": 232}}, "star": {"star_datu": {"width": 232, "height": 232}}, "xuanzhan": {"xuanzhan": {"width": 232, "height": 232}}, "xz2": {"as": {"width": 128, "height": 128}}, "xz3": {"alizi": {"width": 50, "height": 50}}, "xz4": {"xz2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1.36, -1.36, -1.36, -1.36, -1.36, 1.36, 1.36, 1.36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 232}, "xz3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1.36, -1.36, -1.36, -1.36, -1.36, 1.36, 1.36, 1.36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 232}}, "xz5": {"as": {"scaleX": 0.66, "scaleY": 0.66, "rotation": 145, "width": 128, "height": 128}}}}], "animations": {"jin": {"slots": {"ab": {"color": [{"time": 0.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": null}]}, "adian": {"color": [{"color": "ffb31d00", "curve": "stepped"}, {"time": 0.0333, "color": "ffb31d00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.5, "color": "ffb31e00"}], "attachment": [{"time": 0.0333, "name": "<PERSON><PERSON>"}, {"time": 0.5, "name": null}]}, "bao6": {"color": [{"time": 0.7333, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "bao6"}, {"time": 0.9, "name": null}]}, "dian": {"color": [{"time": 0.1333, "color": "ffb31d00"}, {"time": 0.1667, "color": "ffb31fff", "curve": "stepped"}, {"time": 0.4667, "color": "ffb31fff"}, {"time": 0.5333, "color": "ffb31e00"}], "attachment": [{"time": 0.1333, "name": "dian"}, {"time": 0.5333, "name": null}]}, "dian2": {"color": [{"time": 0.2, "color": "ffb31e00"}, {"time": 0.2667, "color": "ffb31fff", "curve": "stepped"}, {"time": 0.7, "color": "ffb31fff"}, {"time": 0.7667, "color": "ffb91900"}], "attachment": [{"time": 0.2, "name": "dian"}, {"time": 0.7667, "name": null}]}, "dian3": {"color": [{"time": 0.1333, "color": "ffb31d00"}, {"time": 0.1667, "color": "ffb31fff", "curve": "stepped"}, {"time": 0.5667, "color": "ffb31fff"}, {"time": 0.6333, "color": "ffb31e00"}], "attachment": [{"time": 0.1333, "name": "dian"}, {"time": 0.6333, "name": null}]}, "gq": {"color": [{"color": "ffffff00", "curve": 0.105, "c2": 0.27, "c3": 0.75}, {"time": 0.0333, "color": "ffe34bff"}, {"time": 0.3, "color": "ffbe08ff"}, {"time": 0.5667, "color": "ffd20700"}], "attachment": [{"name": "gq3"}]}, "gq1": {"color": [{"color": "ffb91900", "curve": "stepped"}, {"time": 0.4, "color": "ffb91900"}, {"time": 0.5667, "color": "ffb91aff"}, {"time": 0.8333, "color": "ffb91900"}], "attachment": [{"time": 0.4, "name": "gq1"}, {"time": 0.8333, "name": null}]}, "gq2": {"color": [{"color": "ffb919c6", "curve": "stepped"}, {"time": 0.1667, "color": "ffb91900"}, {"time": 0.3667, "color": "ffb91aff"}, {"time": 0.6333, "color": "ffb91900"}], "attachment": [{"time": 0.1667, "name": "gq1"}, {"time": 0.6333, "name": null}]}, "guang2": {"color": [{"time": 0.1333, "color": "ffb31bff", "curve": "stepped"}, {"time": 0.4333, "color": "ffb31bff"}, {"time": 0.8667, "color": "ffb31a00"}], "attachment": [{"name": null}, {"time": 0.1333, "name": "guang2"}, {"time": 0.8667, "name": null}]}, "star": {"color": [{"time": 0.3, "color": "ffb31e00"}, {"time": 0.3667, "color": "ffb31fff", "curve": "stepped"}, {"time": 0.7, "color": "ffb31fff"}, {"time": 0.8333, "color": "ffb91900"}], "attachment": [{"name": null}, {"time": 0.3, "name": "star_datu"}, {"time": 0.8333, "name": null}]}, "xuanzhan": {"color": [{"color": "ffb31cff", "curve": "stepped"}, {"time": 0.2333, "color": "ffb31cff"}, {"time": 0.8, "color": "ffb31a00"}], "attachment": [{"time": 0.0333, "name": "xuanzhan"}, {"time": 0.8, "name": null}]}, "xz2": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.5, "color": "ffe589d0"}, {"time": 0.7667, "color": "ffcd0000"}]}, "xz3": {"attachment": [{"name": null}]}, "xz4": {"attachment": [{"name": null}]}, "xz5": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.5, "color": "ffe589d0"}, {"time": 0.7667, "color": "ffcd0000"}], "attachment": [{"name": null}]}}, "bones": {"guang2": {"rotate": [{"angle": 90, "curve": "stepped"}, {"time": 0.1333, "angle": 90}, {"time": 0.5}, {"time": 0.8667, "angle": -90}], "scale": [{"time": 0.1333, "x": 0.1, "y": 0.1, "curve": 0.232, "c2": 0.47, "c3": 0.75}, {"time": 0.4333, "x": 1.2, "y": 1.2}, {"time": 0.8667, "x": 1.66, "y": 1.66}]}, "gq1": {"scale": [{"x": 0.139, "y": 0.139, "curve": "stepped"}, {"time": 0.4}, {"time": 0.8333, "x": 2.317, "y": 2.317}]}, "gq2": {"rotate": [{"angle": 90}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.6333, "x": 2.727, "y": 2.727}]}, "dian": {"scale": [{"time": 0.1333, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.5333, "x": 1.25, "y": 1.25}]}, "dian2": {"rotate": [{"time": 0.2, "angle": -75.9}], "scale": [{"time": 0.2, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.7667, "x": 1.25, "y": 1.25}]}, "adian": {"scale": [{"x": 0.1, "y": 0.1, "curve": "stepped"}, {"time": 0.0333, "x": 0.1, "y": 0.1, "curve": 0, "c2": 0.37, "c3": 0.371, "c4": 0.7}, {"time": 0.0667, "x": 1.17, "y": 1.17, "curve": 0.247, "c2": 0.37, "c3": 0.595, "c4": 0.7}, {"time": 0.1, "x": 0.565, "y": 0.565, "curve": 0.306, "c2": 0.53, "c3": 0.74, "c4": 0.86}, {"time": 0.2, "x": 1.6, "y": 1.6, "curve": 0.368, "c2": 0.68, "c3": 0.789}, {"time": 0.5, "x": 3.26, "y": 3.26}]}, "dian3": {"scale": [{"time": 0.1333, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.6333, "x": 1.594, "y": 1.594}]}, "ab": {"rotate": [{"angle": 45.15, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -90.04}], "scale": [{"x": 0.66, "y": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "x": 1.6, "y": 1.6}]}, "xz2": {"rotate": [{}, {"time": 0.7667, "angle": -92.67}], "scale": [{"x": 0.599, "y": 0.599, "curve": 0.168, "c2": 0.45, "c3": 0.75}, {"time": 0.7667, "x": 3.06, "y": 3.06}]}, "xuanzhan": {"rotate": [{"time": 0.0333}, {"time": 0.5, "angle": -120}, {"time": 0.8, "angle": 165}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0, "curve": 0.232, "c2": 0.47, "c3": 0.75}, {"time": 0.2333}, {"time": 0.8, "x": 1.55, "y": 1.55}]}, "gq": {"scale": [{"x": 0.225, "y": 0.225, "curve": 0.212, "c2": 0.49, "c3": 0.49, "c4": 0.88}, {"time": 0.5667, "x": 2.2, "y": 2.2}]}, "star": {"scale": [{"time": 0.3, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.8333, "x": 1.16, "y": 1.16}]}, "bao6": {"scale": [{"time": 0.3, "x": 0.1, "y": 0.1, "curve": 0.133, "c2": 0.42, "c3": 0.65, "c4": 0.95}, {"time": 0.9, "x": 2.26, "y": 2.26}]}}}, "lan": {"slots": {"ab": {"attachment": [{"name": null}]}, "dian": {"color": [{"time": 0.1, "color": "ffb31d00"}, {"time": 0.1667, "color": "00e7ffff", "curve": "stepped"}, {"time": 0.3667, "color": "00e7ffff"}, {"time": 0.5, "color": "00e7ff00"}], "attachment": [{"time": 0.1, "name": "dian"}, {"time": 0.5, "name": null}]}, "dian2": {"color": [{"time": 0.1667, "color": "ffb31e00"}, {"time": 0.2333, "color": "00e7ffff", "curve": "stepped"}, {"time": 0.5667, "color": "00e7ffff"}, {"time": 0.7, "color": "00e7ff00"}], "attachment": [{"time": 0.1667, "name": "dian"}, {"time": 0.7, "name": null}]}, "dian3": {"color": [{"time": 0.1, "color": "ffb31d00"}, {"time": 0.2, "color": "00e7ffff", "curve": "stepped"}, {"time": 0.4667, "color": "00e7ffff"}, {"time": 0.6, "color": "00e7ff00"}], "attachment": [{"time": 0.1, "name": "dian"}, {"time": 0.6, "name": null}]}, "gq": {"color": [{"color": "ffffff00", "curve": 0.105, "c2": 0.27, "c3": 0.75}, {"time": 0.0667, "color": "0ba1ffff"}, {"time": 0.3333, "color": "0aa1ffff"}, {"time": 0.6333, "color": "09a0ff00"}], "attachment": [{"name": "gq3"}]}, "guang2": {"attachment": [{"name": null}]}, "star": {"color": [{"time": 0.1667, "color": "ffb31e00"}, {"time": 0.2333, "color": "00e7ffff", "curve": "stepped"}, {"time": 0.5667, "color": "00e7ffff"}, {"time": 0.7, "color": "00e7ff00"}], "attachment": [{"name": null}]}, "xz2": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.4667, "color": "89d1ffff"}, {"time": 0.7, "color": "ffffff00"}]}, "xz3": {"color": [{"color": "ffffff00", "curve": 0.105, "c2": 0.27, "c3": 0.75}, {"time": 0.0667, "color": "0ba1ffff"}, {"time": 0.1333, "color": "0aa1ffff", "curve": "stepped"}, {"time": 0.3667, "color": "0aa1ffff"}, {"time": 0.5, "color": "ffffff00"}]}, "xz4": {"color": [{"color": "ffffff00", "curve": 0.105, "c2": 0.27, "c3": 0.75}, {"time": 0.0667, "color": "0ba1ffff"}, {"time": 0.1333, "color": "0aa1ffff", "curve": "stepped"}, {"time": 0.3667, "color": "0aa1ffff"}, {"time": 0.5, "color": "ffffff00"}]}, "xz5": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.4667, "color": "89d1ffff"}, {"time": 0.7, "color": "ffffff00"}]}}, "bones": {"xz": {"scale": [{"x": 0.599, "y": 0.599, "curve": 0.112, "c2": 0.22, "c3": 0.554, "c4": 0.66}, {"time": 0.1333, "x": 36.016, "y": 36.016, "curve": 0.384, "c2": 0.57, "c3": 0.77}, {"time": 0.5, "x": 39.925, "y": 39.925}]}, "xz2": {"rotate": [{"curve": 0, "c2": 0.66, "c3": 0.707, "c4": 0.98}, {"time": 0.7, "angle": -179}], "scale": [{"x": 0.599, "y": 0.599, "curve": 0, "c2": 0.66, "c3": 0.707, "c4": 0.98}, {"time": 0.7, "x": 2.56, "y": 2.56}]}, "gq": {"scale": [{"x": 0.225, "y": 0.225, "curve": 0.212, "c2": 0.49, "c3": 0.49, "c4": 0.88}, {"time": 0.6333, "x": 1.72, "y": 1.72}]}, "dian3": {"scale": [{"time": 0.1, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.6, "x": 0.66, "y": 0.66}]}, "dian": {"scale": [{"time": 0.1, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.5}]}, "dian2": {"rotate": [{"time": 0.1667, "angle": -75.9}], "scale": [{"time": 0.1667, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.7, "x": 0.8, "y": 0.8}]}, "star": {"rotate": [{"time": 0.1667, "angle": -75.9}], "scale": [{"time": 0.1667, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.7, "x": 0.8, "y": 0.8}]}}, "deform": {"default": {"xz4": {"xz3": [{"vertices": [2.44022, -2.44022, -2.44022, -2.44022, -2.44022, 2.44022, 2.44022, 2.44022]}], "xz2": [{"vertices": [2.44022, -2.44022, -2.44022, -2.44022, -2.44022, 2.44022, 2.44022, 2.44022]}]}}}}, "lv": {"slots": {"ab": {"attachment": [{"name": null}]}, "gq": {"color": [{"color": "ffffff00", "curve": 0.105, "c2": 0.27, "c3": 0.75}, {"time": 0.0667, "color": "5bff0aff"}, {"time": 0.2333, "color": "5adb12ff"}, {"time": 0.5333, "color": "36dd1300"}], "attachment": [{"name": "gq3"}]}, "gq1": {"color": [{"color": "ffb91900"}]}, "guang2": {"attachment": [{"name": null}]}, "star": {"attachment": [{"name": null}]}, "xz2": {"color": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.4667, "color": "8bff2dff"}, {"time": 0.7, "color": "67ff2900"}]}, "xz3": {"color": [{"color": "ffffff00", "curve": 0.105, "c2": 0.27, "c3": 0.75}, {"time": 0.0667, "color": "5bff0bff"}, {"time": 0.1667, "color": "5adb12ff", "curve": "stepped"}, {"time": 0.3667, "color": "5adb12ff"}, {"time": 0.5, "color": "36dd1300"}]}, "xz4": {"color": [{"color": "ffffff00", "curve": 0.105, "c2": 0.27, "c3": 0.75}, {"time": 0.0667, "color": "5bff0bff"}, {"time": 0.1667, "color": "5adb12ff", "curve": "stepped"}, {"time": 0.3667, "color": "5adb12ff"}, {"time": 0.5, "color": "36dd1300"}], "attachment": [{"name": "xz2"}]}, "xz5": {"color": [{"color": "ffffff00"}], "attachment": [{"name": null}]}}, "bones": {"xz": {"scale": [{"x": 0.599, "y": 0.599, "curve": 0.112, "c2": 0.22, "c3": 0.554, "c4": 0.66}, {"time": 0.1667, "x": 33.27, "y": 33.27, "curve": 0.384, "c2": 0.57, "c3": 0.77}, {"time": 0.5, "x": 37.263, "y": 37.263}]}, "xz2": {"rotate": [{"curve": 0.1, "c2": 0.68, "c3": 0.75}, {"time": 0.7, "angle": -160}], "scale": [{"x": 0.599, "y": 0.599, "curve": 0.1, "c2": 0.68, "c3": 0.75}, {"time": 0.7, "x": 2.06, "y": 2.06}]}, "gq": {"scale": [{"x": 0.225, "y": 0.225, "curve": 0.069, "c2": 0.22, "c3": 0.49, "c4": 0.91}, {"time": 0.5333, "x": 1.33, "y": 1.33}]}, "gq1": {"scale": [{"x": 0.139, "y": 0.139}]}}, "deform": {"default": {"xz4": {"xz3": [{"vertices": [2.44022, -2.44022, -2.44022, -2.44022, -2.44022, 2.44022, 2.44022, 2.44022]}], "xz2": [{"vertices": [2.44022, -2.44022, -2.44022, -2.44022, -2.44022, 2.44022, 2.44022, 2.44022]}]}}}}, "zi": {"slots": {"ab": {"attachment": [{"name": null}]}, "dian": {"color": [{"time": 0.1, "color": "ffffff00"}, {"time": 0.1667, "color": "ea2bffff", "curve": "stepped"}, {"time": 0.3667, "color": "ea2bffff"}, {"time": 0.5, "color": "e500ffff"}], "attachment": [{"time": 0.1, "name": "dian"}, {"time": 0.5, "name": null}]}, "dian2": {"color": [{"time": 0.1667, "color": "ffffff00"}, {"time": 0.2333, "color": "e600ffff", "curve": "stepped"}, {"time": 0.5667, "color": "e600ffff"}, {"time": 0.7, "color": "e500ffff"}], "attachment": [{"time": 0.1667, "name": "dian"}, {"time": 0.7, "name": null}]}, "dian3": {"color": [{"time": 0.1, "color": "ffffff00"}, {"time": 0.2, "color": "e81dffff", "curve": "stepped"}, {"time": 0.4667, "color": "e81dffff"}, {"time": 0.6, "color": "e607ffff"}], "attachment": [{"time": 0.1, "name": "dian"}, {"time": 0.6, "name": null}]}, "gq": {"color": [{"color": "ffffff00", "curve": 0.105, "c2": 0.27, "c3": 0.75}, {"time": 0.0667, "color": "c73bffff"}, {"time": 0.3333, "color": "9c09ffff"}, {"time": 0.6333, "color": "cc08ff00"}], "attachment": [{"name": "gq3"}]}, "gq1": {"color": [{"time": 0.0667, "color": "ea74ff00"}, {"time": 0.1333, "color": "cd65ffff"}, {"time": 0.3667, "color": "dc19ffff"}, {"time": 0.5, "color": "eb19ff00"}], "attachment": [{"time": 0.0667, "name": "gq1"}, {"time": 0.5, "name": null}]}, "gq2": {"color": [{"time": 0.2333, "color": "ea74ff00"}, {"time": 0.3, "color": "cd65ffff"}, {"time": 0.4667, "color": "dc19ffff"}, {"time": 0.6667, "color": "eb19ff00"}], "attachment": [{"time": 0.2333, "name": "gq1"}, {"time": 0.6667, "name": null}]}, "guang2": {"attachment": [{"name": null}]}, "star": {"color": [{"time": 0.2333, "color": "ffffff00"}, {"time": 0.3333, "color": "e600ffff", "curve": "stepped"}, {"time": 0.4667, "color": "e600ffff"}, {"time": 0.7, "color": "e500ffff"}], "attachment": [{"name": null}, {"time": 0.2333, "name": "star_datu"}, {"time": 0.7, "name": null}]}, "xuanzhan": {"color": [{"color": "d232ff00"}, {"time": 0.2, "color": "d233ffff"}, {"time": 0.7, "color": "d232ff00"}], "attachment": [{"name": "xuanzhan"}]}, "xz2": {"color": [{"color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.5, "color": "ee89ffff"}, {"time": 0.7333, "color": "f300ff00"}]}, "xz3": {"color": [{"color": "ffffff00", "curve": 0.105, "c2": 0.27, "c3": 0.75}, {"time": 0.0667, "color": "c95fffff"}, {"time": 0.1333, "color": "cc43ffff", "curve": "stepped"}, {"time": 0.3667, "color": "cc43ffff"}, {"time": 0.5, "color": "ff35f700"}]}, "xz4": {"color": [{"color": "ffffff00", "curve": 0.105, "c2": 0.27, "c3": 0.75}, {"time": 0.0667, "color": "c149ffff"}, {"time": 0.1333, "color": "c009ffff", "curve": "stepped"}, {"time": 0.3667, "color": "c009ffff"}, {"time": 0.5, "color": "c73bff00"}]}, "xz5": {"color": [{"color": "ffffff00"}], "attachment": [{"name": null}]}}, "bones": {"xz": {"scale": [{"x": 0.599, "y": 0.599, "curve": 0.112, "c2": 0.22, "c3": 0.554, "c4": 0.66}, {"time": 0.1333, "x": 36.016, "y": 36.016, "curve": 0.384, "c2": 0.57, "c3": 0.77}, {"time": 0.5, "x": 39.925, "y": 39.925}]}, "xz2": {"rotate": [{}, {"time": 0.7333, "angle": -92.67}], "scale": [{"x": 0.599, "y": 0.599, "curve": 0.168, "c2": 0.45, "c3": 0.75}, {"time": 0.7333, "x": 2.86, "y": 2.86}]}, "gq": {"scale": [{"x": 0.225, "y": 0.225, "curve": 0.212, "c2": 0.49, "c3": 0.49, "c4": 0.88}, {"time": 0.6333, "x": 2, "y": 2}]}, "dian3": {"scale": [{"time": 0.1, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.6, "x": 0.86, "y": 0.86}]}, "dian": {"scale": [{"time": 0.1, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.5, "x": 1.12, "y": 1.12}]}, "dian2": {"rotate": [{"time": 0.1667, "angle": -75.9}], "scale": [{"time": 0.1667, "x": 0.1, "y": 0.1, "curve": 0.141, "c2": 0.6, "c3": 0.75}, {"time": 0.7}]}, "gq2": {"scale": [{"curve": "stepped"}, {"time": 0.2333, "x": 0.143, "y": 0.143}, {"time": 0.6667, "x": 1.841, "y": 1.841}]}, "gq1": {"scale": [{"curve": "stepped"}, {"time": 0.0667, "x": 0.143, "y": 0.143}, {"time": 0.5, "x": 1.841, "y": 1.841}]}, "xuanzhan": {"rotate": [{}, {"time": 0.7, "angle": -92.67}], "scale": [{"x": 0.1, "y": 0.1, "curve": 0.168, "c2": 0.45, "c3": 0.75}, {"time": 0.7}]}, "star": {"rotate": [{"time": 0.2333, "angle": -7.67}], "scale": [{"time": 0.2333, "x": 0.1, "y": 0.1, "curve": 0.169, "c2": 0.44, "c3": 0.65, "c4": 0.95}, {"time": 0.7, "x": 0.82, "y": 0.82}]}}, "deform": {"default": {"xz4": {"xz3": [{"vertices": [2.44022, -2.44022, -2.44022, -2.44022, -2.44022, 2.44022, 2.44022, 2.44022]}], "xz2": [{"vertices": [2.44022, -2.44022, -2.44022, -2.44022, -2.44022, 2.44022, 2.44022, 2.44022]}]}}}}}}, "textures": [{"__uuid__": "9f56b29f-2fc9-413b-bc05-66876e33fbb9@6c48a", "__expectedType__": "cc.Texture2D"}], "textureNames": ["liwugaibian.png"], "scale": 1, "_atlasText": "\nliwugaibian.png\nsize: 582,228\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nab\n  rotate: false\n  xy: 301, 2\n  size: 40, 40\n  orig: 40, 40\n  offset: 0, 0\n  index: -1\nadian\n  rotate: false\n  xy: 2, 10\n  size: 102, 98\n  orig: 111, 107\n  offset: 4, 5\n  index: -1\nalizi\n  rotate: false\n  xy: 433, 91\n  size: 17, 17\n  orig: 25, 25\n  offset: 4, 4\n  index: -1\nas\n  rotate: false\n  xy: 301, 44\n  size: 64, 64\n  orig: 64, 64\n  offset: 0, 0\n  index: -1\nbao6\n  rotate: false\n  xy: 367, 44\n  size: 64, 64\n  orig: 64, 64\n  offset: 0, 0\n  index: -1\ndian\n  rotate: false\n  xy: 206, 18\n  size: 93, 90\n  orig: 116, 116\n  offset: 10, 14\n  index: -1\ngq1\n  rotate: false\n  xy: 2, 110\n  size: 116, 116\n  orig: 116, 116\n  offset: 0, 0\n  index: -1\ngq3\n  rotate: false\n  xy: 106, 10\n  size: 98, 98\n  orig: 116, 116\n  offset: 9, 9\n  index: -1\nguang2\n  rotate: true\n  xy: 474, 117\n  size: 109, 106\n  orig: 116, 116\n  offset: 2, 5\n  index: -1\nstar_datu\n  rotate: false\n  xy: 474, 9\n  size: 103, 106\n  orig: 116, 116\n  offset: 6, 5\n  index: -1\nxuanzhan\n  rotate: false\n  xy: 120, 110\n  size: 116, 116\n  orig: 116, 116\n  offset: 0, 0\n  index: -1\nxz2\n  rotate: false\n  xy: 238, 110\n  size: 116, 116\n  orig: 116, 116\n  offset: 0, 0\n  index: -1\nxz3\n  rotate: false\n  xy: 356, 110\n  size: 116, 116\n  orig: 116, 116\n  offset: 0, 0\n  index: -1\n"}