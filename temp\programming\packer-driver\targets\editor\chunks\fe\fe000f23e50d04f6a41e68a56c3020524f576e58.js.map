{"version": 3, "sources": ["file:///F:/SVN/plinko/BasePro/DemoMoveV3/assets/scripts/ui/elixir/ElixirView.ts"], "names": ["_decorator", "Component", "instantiate", "Label", "Node", "Prefab", "sp", "Sprite", "SpriteFrame", "tween", "UITransform", "Vec3", "lobby", "yy", "oops", "ELIXIR_MAP_LIST", "ITEM", "itemcfg", "ElixirViewItem", "DigTreasure", "Message", "re<PERSON><PERSON><PERSON><PERSON>", "config", "elixirUnsealCfg", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Skeleton", "_kaidingData", "_luziNoot", "_resultPrefab", "_linePrefab", "_itemPrefab", "_giftPrefab", "_moveNum", "_openType", "_openPosList", "_isSyncBlank", "onLoad", "on", "onKaidingBegin", "onKaidingFinish", "event", "events", "MyPropsChanged", "onUpdateProps", "skeLuzi", "node", "parent", "ndResultArea", "children", "destroyAllChildren", "ndLines", "ndItem", "ndItems", "ndGift", "onDestroy", "off", "onAdded", "data", "console", "log", "kaidingData", "game", "getNextAutoKaiDingInfo", "getEMapType", "ndElixirArea", "lMapType", "getLMapType", "itemIdList", "for<PERSON>ach", "itemId", "itemDataCfg", "getDataByIndex", "itemNode", "pbItem", "<PERSON><PERSON><PERSON><PERSON>", "itemComp", "getComponent", "init", "updateTimer", "updateLingqi", "ndBeforeKaiDing", "active", "ndAfterKaiDing", "ndZiYangArea", "ndOpenArea", "setAnimation", "onEnable", "start", "update", "dt", "ballSlotPairs", "ballName", "slotName", "pair", "index", "ballNode", "getChildByName", "slotNode", "skOpenBlankArea", "slotWorldPos", "convertToWorldSpaceAR", "ZERO", "targetLocalPos", "convertToNodeSpaceAR", "setPosition", "onCloseClick", "gui", "removeByNode", "onBtnManulKaiDing", "m_gameNet", "Send_ReqOnceKaiDing", "onBtnUseAll", "debug", "moveElixirBallsToSlots", "curTime", "Date", "now", "getServerTimeOffset", "timeData", "Math", "floor", "nextKaiDingTime", "toNumber", "minutes", "remainingSeconds", "formattedMinutes", "toString", "length", "formattedSeconds", "labTimer", "string", "eMapType", "count", "eMT_Shan2", "myBag", "getPropCountByID", "LINGQI_1", "eMT_Dong2", "LINGQI_2", "eMT_Ling2", "LINGQI_3", "labLingqi", "scheduleOnce", "elixirList", "analyzeArrayCombination", "kaidingPlan", "kaiDingResults", "result", "elixirId", "loadSp", "res", "zhanWeiGiftAList", "gift", "costCount", "xianDanCostCounts", "loadResultItem", "iconPath", "spriteNode", "load", "bundleName", "err", "spFrame", "spriteFrame", "elixirIcon", "elixirCount", "elixirDataCfg", "giftIcon", "giftCount", "giftDataCfg", "parseInt", "giftId", "giftNum", "duration", "delayBetween", "position", "clone", "moveBallToSlot", "delay", "name", "x", "y", "to", "easing", "call", "switchToAfterKaiDing", "openNum", "slotPairs", "i", "lineNode", "dataList", "getData", "j", "giftNode", "numbers", "countMap", "Map", "num", "set", "get", "counts", "Array", "from", "values", "sort", "a", "b"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AACtGC,MAAAA,K,WAAAA,K;AAAOC,MAAAA,E,WAAAA,E;;AACPC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,e,iBAAAA,e;AAAiBC,MAAAA,I,iBAAAA,I;;AACjBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,e,kBAAAA,e;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBzB,U;;4BAGjB0B,U,WADZF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACpB,MAAD,C,UAERoB,QAAQ,CAACrB,IAAD,C,UAERqB,QAAQ,CAACtB,KAAD,C,UAERsB,QAAQ,CAACtB,KAAD,C,UAGRsB,QAAQ,CAACrB,IAAD,C,UAERqB,QAAQ,CAACrB,IAAD,C,UAERqB,QAAQ,CAACrB,IAAD,C,UAERqB,QAAQ,CAACrB,IAAD,C,WAGRqB,QAAQ,CAACrB,IAAD,C,WAERqB,QAAQ,CAACnB,EAAE,CAACqB,QAAJ,C,WAERF,QAAQ,CAACrB,IAAD,C,WAERqB,QAAQ,CAACrB,IAAD,C,WAGRqB,QAAQ,CAACnB,EAAE,CAACqB,QAAJ,C,2BA9Bb,MACaD,UADb,SACgCzB,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAgC9B2B,YAhC8B,GAgCmB,IAhCnB;AAAA,eAkC9BC,SAlC8B,GAkCZ,IAlCY;AAAA,eAoC9BC,aApC8B,GAoCR,IApCQ;AAAA,eAqC9BC,WArC8B,GAqCV,IArCU;AAAA,eAsC9BC,WAtC8B,GAsCV,IAtCU;AAAA,eAuC9BC,WAvC8B,GAuCV,IAvCU;AAAA,eAyC9BC,QAzC8B,GAyCX,CAzCW;AAAA,eA0C9BC,SA1C8B,GA0C0B,IA1C1B;AAAA,eA4C9BC,YA5C8B,GA4CP,EA5CO;AAAA,eA6C9BC,YA7C8B,GA6Cf,KA7Ce;AAAA;;AA+CtCC,QAAAA,MAAM,GAAG;AACL;AAAA;AAAA,kCAAQC,EAAR,CAAW,8BAAX,EAA2C,KAAKC,cAAhD,EAAgE,IAAhE;AACA;AAAA;AAAA,kCAAQD,EAAR,CAAW,+BAAX,EAA4C,KAAKE,eAAjD,EAAkE,IAAlE;AACA;AAAA;AAAA,kCAAQF,EAAR,CAAW,8BAAX,EAA2C,KAAKC,cAAhD,EAAgE,IAAhE;AACA;AAAA;AAAA,kCAAQD,EAAR,CAAW,+BAAX,EAA4C,KAAKE,eAAjD,EAAkE,IAAlE;AAEA;AAAA;AAAA,wBAAGC,KAAH,CAASH,EAAT,CAAY,IAAZ,EAAkB;AAAA;AAAA,8BAAMI,MAAN,CAAaC,cAA/B,EAA+C,KAAKC,aAApD;AAEA,eAAKhB,SAAL,GAAiB,KAAKiB,OAAL,CAAaC,IAAb,CAAkBC,MAAnC;AAEA,eAAKlB,aAAL,GAAqB5B,WAAW,CAAC,KAAK+C,YAAL,CAAkBC,QAAlB,CAA2B,CAA3B,CAAD,CAAhC;AACA,eAAKD,YAAL,CAAkBE,kBAAlB;AAEA,eAAKpB,WAAL,GAAmB7B,WAAW,CAAC,KAAKkD,OAAL,CAAaF,QAAb,CAAsB,CAAtB,CAAD,CAA9B;AACA,eAAKE,OAAL,CAAaD,kBAAb;AACA,gBAAME,MAAM,GAAG,KAAKC,OAAL,CAAaJ,QAAb,CAAsB,CAAtB,CAAf;AACA,gBAAMK,MAAM,GAAGF,MAAM,CAACH,QAAP,CAAgB,CAAhB,CAAf;AACA,eAAKjB,WAAL,GAAmB/B,WAAW,CAACqD,MAAD,CAA9B;AACAF,UAAAA,MAAM,CAACF,kBAAP;AACA,eAAKnB,WAAL,GAAmB9B,WAAW,CAACmD,MAAD,CAA9B;AACA,eAAKC,OAAL,CAAaH,kBAAb;AACH;;AAEDK,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQC,GAAR,CAAY,8BAAZ,EAA4C,KAAKjB,cAAjD,EAAiE,IAAjE;AACA;AAAA;AAAA,kCAAQiB,GAAR,CAAY,+BAAZ,EAA6C,KAAKhB,eAAlD,EAAmE,IAAnE;AACA;AAAA;AAAA,kCAAQgB,GAAR,CAAY,8BAAZ,EAA4C,KAAKjB,cAAjD,EAAiE,IAAjE;AACA;AAAA;AAAA,kCAAQiB,GAAR,CAAY,+BAAZ,EAA6C,KAAKhB,eAAlD,EAAmE,IAAnE;AAEA;AAAA;AAAA,wBAAGC,KAAH,CAASe,GAAT,CAAa;AAAA;AAAA,8BAAMd,MAAN,CAAaC,cAA1B,EAA0C,KAAKC,aAA/C;AACH;;AAEDa,QAAAA,OAAO,CAACC,IAAD,EAAO;AACVC,UAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCF,IAArC;AACA,gBAAMG,WAAW,GAAG;AAAA;AAAA,4BAAKC,IAAL,CAAUC,sBAAV,CAAiC;AAAA;AAAA,4BAAKD,IAAL,CAAUE,WAAV,EAAjC,CAApB;AACA,eAAKrC,YAAL,GAAoBkC,WAApB;AAEA,eAAKI,YAAL,CAAkBf,kBAAlB;AAEA,gBAAMgB,QAAQ,GAAG;AAAA;AAAA,4BAAKJ,IAAL,CAAUK,WAAV,EAAjB;AACA,gBAAMC,UAAU,GAAG;AAAA;AAAA,kDAAgBF,QAAhB,CAAnB;AACAE,UAAAA,UAAU,CAACC,OAAX,CAAmBC,MAAM,IAAI;AACzB,kBAAMC,WAAW,GAAG;AAAA;AAAA,oCAAQC,cAAR,CAAuBF,MAAvB,CAApB;AACA,kBAAMG,QAAQ,GAAGxE,WAAW,CAAC,KAAKyE,MAAN,CAA5B;AACA,iBAAKT,YAAL,CAAkBU,QAAlB,CAA2BF,QAA3B;AAEA,kBAAMG,QAAQ,GAAGH,QAAQ,CAACI,YAAT;AAAA;AAAA,iDAAjB;AACAD,YAAAA,QAAQ,CAACE,IAAT,CAAcP,WAAd;AACH,WAPD;AASA,eAAKQ,WAAL;AACA,eAAKC,YAAL;AAEA,eAAKC,eAAL,CAAqBC,MAArB,GAA8B,IAA9B;AACA,eAAKC,cAAL,CAAoBD,MAApB,GAA6B,KAA7B;AACA,eAAKE,YAAL,CAAkBF,MAAlB,GAA2B,IAA3B;AACA,eAAKlC,YAAL,CAAkBkC,MAAlB,GAA2B,KAA3B;AACA,eAAKG,UAAL,CAAgBH,MAAhB,GAAyB,KAAzB;AACA,eAAKtD,SAAL,CAAesD,MAAf,GAAwB,IAAxB;AACA,eAAKrC,OAAL,CAAayC,YAAb,CAA0B,CAA1B,EAA6B,MAA7B,EAAqC,IAArC;AACA,eAAKrD,QAAL,GAAgB,CAAhB;AACA,eAAKC,SAAL,GAAiB,IAAjB;AACA,eAAKE,YAAL,GAAoB,KAApB;AACH;;AAEDmD,QAAAA,QAAQ,GAAS;AACb5B,UAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ;AACH;;AAED4B,QAAAA,KAAK,GAAG;AACJ7B,UAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ;AACH;;AAED6B,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,eAAKX,WAAL;;AAEA,cAAI,KAAK3C,YAAT,EAAuB;AACnB;AACA,kBAAMuD,aAAa,GAAG,CAClB;AAAEC,cAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,cAAAA,QAAQ,EAAE;AAAxC,aADkB,EAElB;AAAED,cAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,cAAAA,QAAQ,EAAE;AAAxC,aAFkB,EAGlB;AAAED,cAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,cAAAA,QAAQ,EAAE;AAAxC,aAHkB,EAIlB;AAAED,cAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,cAAAA,QAAQ,EAAE;AAAxC,aAJkB,CAAtB,CAFmB,CASnB;;AACAF,YAAAA,aAAa,CAACtB,OAAd,CAAsB,CAACyB,IAAD,EAAOC,KAAP,KAAiB;AACnC,oBAAMC,QAAQ,GAAG,KAAKX,UAAL,CAAgBY,cAAhB,CAA+BH,IAAI,CAACF,QAApC,CAAjB;AACA,oBAAMM,QAAQ,GAAG,KAAKC,eAAL,CAAqBrD,IAArB,CAA0BmD,cAA1B,CAAyCH,IAAI,CAACD,QAA9C,CAAjB;AACA,oBAAMO,YAAY,GAAGF,QAAQ,CAACrB,YAAT,CAAsBpE,WAAtB,EAAoC4F,qBAApC,CAA0D3F,IAAI,CAAC4F,IAA/D,CAArB;AACA,oBAAMC,cAAc,GAAGP,QAAQ,CAACjD,MAAT,CAAiB8B,YAAjB,CAA8BpE,WAA9B,EAA4C+F,oBAA5C,CAAiEJ,YAAjE,CAAvB;AACAJ,cAAAA,QAAQ,CAACS,WAAT,CAAqBF,cAArB;AACH,aAND;AAOH;AACJ;;AAESG,QAAAA,YAAY,GAAG;AACrB;AAAA;AAAA,4BAAKC,GAAL,CAASC,YAAT,CAAsB,KAAK9D,IAA3B;AACH;;AAES+D,QAAAA,iBAAiB,GAAG;AAC1B;AAAA;AAAA,4BAAK/C,IAAL,CAAUgD,SAAV,CAAoBC,mBAApB;AACH;;AAESC,QAAAA,WAAW,GAAG;AACpBrD,UAAAA,OAAO,CAACsD,KAAR,CAAc,aAAd,EADoB,CAEpB;;AACA,eAAKC,sBAAL;AACH;;AAEOnC,QAAAA,WAAW,GAAG;AAClB,cAAI,KAAKpD,YAAT,EAAuB;AACnB,kBAAMwF,OAAO,GAAGC,IAAI,CAACC,GAAL,KAAa;AAAA;AAAA,8BAAKvD,IAAL,CAAUwD,mBAAV,EAA7B;AACA,kBAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAL,CAAW,CAAC,KAAK9F,YAAL,CAAkB+F,eAAlB,CAAkCC,QAAlC,KAA+C,IAA/C,GAAsDR,OAAvD,IAAkE,IAA7E,CAAjB;AACA,kBAAMS,OAAe,GAAGJ,IAAI,CAACC,KAAL,CAAWF,QAAQ,GAAG,EAAtB,CAAxB;AACA,kBAAMM,gBAAwB,GAAGN,QAAQ,GAAG,EAA5C;AACA,kBAAMO,gBAAwB,GAAGF,OAAO,CAACG,QAAR,GAAmBC,MAAnB,GAA4B,CAA5B,GAAiC,IAAGJ,OAAO,CAACG,QAAR,EAAmB,EAAvD,GAA2DH,OAAO,CAACG,QAAR,EAA5F;AACA,kBAAME,gBAAwB,GAAGJ,gBAAgB,CAACE,QAAjB,GAA4BC,MAA5B,GAAqC,CAArC,GAA0C,IAAGH,gBAAgB,CAACE,QAAjB,EAA4B,EAAzE,GAA6EF,gBAAgB,CAACE,QAAjB,EAA9G;AACA,iBAAKG,QAAL,CAAcC,MAAd,GAAuB,WAAWP,OAAO,IAAI,CAAX,IAAgBC,gBAAgB,IAAI,CAApC,GAAwC,OAAxC,GAAmD,GAAEC,gBAAiB,IAAGG,gBAAiB,EAArG,CAAvB;AACH;AACJ;;AAEOjD,QAAAA,YAAY,GAAG;AACnB,gBAAMoD,QAAQ,GAAG;AAAA;AAAA,4BAAKtE,IAAL,CAAUE,WAAV,EAAjB;AACA,cAAIqE,KAAK,GAAG,CAAZ;;AACA,kBAAQD,QAAR;AACI,iBAAK;AAAA;AAAA,4CAAYA,QAAZ,CAAqBE,SAA1B;AACID,cAAAA,KAAK,GAAG;AAAA;AAAA,kCAAME,KAAN,CAAYC,gBAAZ,CAA6B;AAAA;AAAA,gCAAKC,QAAlC,IAA8C,CAAtD;AACA;;AACJ,iBAAK;AAAA;AAAA,4CAAYL,QAAZ,CAAqBM,SAA1B;AACIL,cAAAA,KAAK,GAAG;AAAA;AAAA,kCAAME,KAAN,CAAYC,gBAAZ,CAA6B;AAAA;AAAA,gCAAKG,QAAlC,IAA8C,CAAtD;AACA;;AACJ,iBAAK;AAAA;AAAA,4CAAYP,QAAZ,CAAqBQ,SAA1B;AACIP,cAAAA,KAAK,GAAG;AAAA;AAAA,kCAAME,KAAN,CAAYC,gBAAZ,CAA6B;AAAA;AAAA,gCAAKK,QAAlC,IAA8C,CAAtD;AACA;AATR;;AAWA,eAAKC,SAAL,CAAeX,MAAf,GAAwBE,KAAK,CAACN,QAAN,EAAxB;AACH;;AAEOxF,QAAAA,cAAc,CAACE,KAAD,EAAQiB,IAAR,EAA8F;AAChH;AACA;AACA;AACA;AACA;AAEA,eAAKb,OAAL,CAAayC,YAAb,CAA0B,CAA1B,EAA6B,KAA7B,EAAoC,KAApC;AACA,eAAKyD,YAAL,CAAkB,MAAM;AACpB,iBAAK1D,UAAL,CAAgBH,MAAhB,GAAyB,IAAzB;AACA,iBAAKgC,sBAAL;AACH,WAHD,EAGG,GAHH;AAIH;;AAEO1E,QAAAA,eAAe,CAACC,KAAD,EAAQiB,IAAR,EAAiH;AACpI,gBAAMsF,UAAU,GAAG;AAAA;AAAA,kDAAgB;AAAA;AAAA,4BAAKlF,IAAL,CAAUK,WAAV,EAAhB,CAAnB;AAEA,eAAKjC,SAAL,GAAiB,KAAK+G,uBAAL,CAA6BvF,IAAI,CAACwF,WAAL,CAAiBC,cAA9C,CAAjB;AACAxF,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAK1B,SAA9B;AACAwB,UAAAA,IAAI,CAACwF,WAAL,CAAiBC,cAAjB,CAAgC9E,OAAhC,CAAwC,CAAC+E,MAAD,EAASrD,KAAT,KAAmB;AACvD,kBAAMsD,QAAQ,GAAGL,UAAU,CAACI,MAAD,CAA3B;AACA,kBAAM/I,EAAE,GAAG,KAAKgF,UAAL,CAAgBpC,QAAhB,CAAyB8C,KAAzB,EAAgClB,YAAhC,CAA6CvE,MAA7C,CAAX;AACA,kBAAMiE,WAAW,GAAG;AAAA;AAAA,oCAAQC,cAAR,CAAuB6E,QAAvB,CAApB;AACA,iBAAKC,MAAL,CAAa,eAAc/E,WAAW,CAACgF,GAAI,cAA3C,EAA0DlJ,EAA1D;AACH,WALD;AAOA,eAAK2C,YAAL,CAAkBE,kBAAlB;AACAQ,UAAAA,IAAI,CAACwF,WAAL,CAAiBM,gBAAjB,CAAkCnF,OAAlC,CAA0C,CAACoF,IAAD,EAAO1D,KAAP,KAAiB;AACvD,kBAAMsD,QAAQ,GAAGL,UAAU,CAACjD,KAAD,CAA3B;AACA,kBAAM2D,SAAS,GAAGhG,IAAI,CAACwF,WAAL,CAAiBS,iBAAjB,CAAmC5D,KAAnC,KAA6C,CAA/D;AACA,iBAAK6D,cAAL,CAAoBP,QAApB,EAA8BK,SAA9B,EAAyCD,IAAzC;AACH,WAJD;AAKH;;AAEOH,QAAAA,MAAM,CAACO,QAAD,EAAmBC,UAAnB,EAAuC;AACjD;AAAA;AAAA,sCAAUC,IAAV,CAAe;AAAA;AAAA,gCAAOjG,IAAP,CAAYkG,UAA3B,EAAuCH,QAAvC,EAAiDtJ,WAAjD,EAA8D,CAAC0J,GAAD,EAAMC,OAAN,KAA+B;AACzF,gBAAID,GAAJ,EAAS;AACL;AACH;;AACDH,YAAAA,UAAU,CAACK,WAAX,GAAyBD,OAAzB;AACH,WALD;AAMH;;AAEON,QAAAA,cAAc,CAACP,QAAD,EAAmBhB,KAAnB,EAAkCoB,IAAlC,EAAoE;AACtF,gBAAMrG,MAAM,GAAGnD,WAAW,CAAC,KAAK4B,aAAN,CAA1B;AACA,eAAKmB,YAAL,CAAkB2B,QAAlB,CAA2BvB,MAA3B;AAEA,gBAAMgH,UAAU,GAAGhH,MAAM,CAAC6C,cAAP,CAAsB,aAAtB,EAAqCpB,YAArC,CAAkDvE,MAAlD,CAAnB;AACA,gBAAM+J,WAAW,GAAGjH,MAAM,CAAC6C,cAAP,CAAsB,cAAtB,EAAsCpB,YAAtC,CAAmD3E,KAAnD,CAApB;AACA,gBAAMoK,aAAa,GAAG;AAAA;AAAA,kCAAQ9F,cAAR,CAAuB6E,QAAvB,CAAtB;AACA,eAAKC,MAAL,CAAa,eAAcgB,aAAa,CAACf,GAAI,cAA7C,EAA4Da,UAA5D;AACAC,UAAAA,WAAW,CAAClC,MAAZ,GAAsB,GAAEE,KAAM,EAA9B;AAEA,gBAAMkC,QAAQ,GAAGnH,MAAM,CAAC6C,cAAP,CAAsB,WAAtB,EAAmCpB,YAAnC,CAAgDvE,MAAhD,CAAjB;AACA,gBAAMkK,SAAS,GAAGpH,MAAM,CAAC6C,cAAP,CAAsB,YAAtB,EAAoCpB,YAApC,CAAiD3E,KAAjD,CAAlB;AACA,gBAAMuK,WAAW,GAAG;AAAA;AAAA,kCAAQjG,cAAR,CAAuBkG,QAAQ,CAACjB,IAAI,CAACkB,MAAN,CAA/B,CAApB;AACA,eAAKrB,MAAL,CAAa,aAAYmB,WAAW,CAAClB,GAAI,cAAzC,EAAwDgB,QAAxD;AACAC,UAAAA,SAAS,CAACrC,MAAV,GAAoB,GAAEsB,IAAI,CAACmB,OAAQ,EAAnC;AACH;;AAEOhI,QAAAA,aAAa,GAAG;AACpB,eAAKoC,YAAL;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWkC,QAAAA,sBAAsB,CAAC2D,QAAgB,GAAG,GAApB,EAAyBC,YAAoB,GAAG,GAAhD,EAAqD;AAC9EnH,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ,EAD8E,CAG9E;;AACA,gBAAM+B,aAAa,GAAG,CAClB;AAAEC,YAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,YAAAA,QAAQ,EAAE;AAAxC,WADkB,EAElB;AAAED,YAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,YAAAA,QAAQ,EAAE;AAAxC,WAFkB,EAGlB;AAAED,YAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,YAAAA,QAAQ,EAAE;AAAxC,WAHkB,EAIlB;AAAED,YAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,YAAAA,QAAQ,EAAE;AAAxC,WAJkB,CAAtB,CAJ8E,CAW9E;;AACAF,UAAAA,aAAa,CAACtB,OAAd,CAAsB,CAACyB,IAAD,EAAOC,KAAP,KAAiB;AACnC,kBAAMC,QAAQ,GAAG,KAAKX,UAAL,CAAgBY,cAAhB,CAA+BH,IAAI,CAACF,QAApC,CAAjB;;AACA,gBAAI,KAAKzD,YAAL,CAAkB4D,KAAlB,CAAJ,EAA8B;AAC1BC,cAAAA,QAAQ,CAACS,WAAT,CAAqB,KAAKtE,YAAL,CAAkB4D,KAAlB,CAArB;AACH;;AACD,kBAAMG,QAAQ,GAAG,KAAKC,eAAL,CAAqBrD,IAArB,CAA0BmD,cAA1B,CAAyCH,IAAI,CAACD,QAA9C,CAAjB;AACA,iBAAK1D,YAAL,CAAkB4D,KAAlB,IAA2BC,QAAQ,CAAC+E,QAAT,CAAkBC,KAAlB,EAA3B;AACA,iBAAKC,cAAL,CAAoBjF,QAApB,EAA8BE,QAA9B,EAAwCH,KAAK,GAAG+E,YAAhD,EAA8DD,QAA9D;AACH,WARD;AASH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACYI,QAAAA,cAAc,CAACjF,QAAD,EAAiBE,QAAjB,EAAiCgF,KAAa,GAAG,CAAjD,EAAoDL,QAAgB,GAAG,GAAvE,EAA4E;AAC9F;AACA,gBAAMzE,YAAY,GAAGF,QAAQ,CAACrB,YAAT,CAAsBpE,WAAtB,EAAoC4F,qBAApC,CAA0D3F,IAAI,CAAC4F,IAA/D,CAArB,CAF8F,CAG9F;;AACA,gBAAMC,cAAc,GAAGP,QAAQ,CAACjD,MAAT,CAAiB8B,YAAjB,CAA8BpE,WAA9B,EAA4C+F,oBAA5C,CAAiEJ,YAAjE,CAAvB;AAEAzC,UAAAA,OAAO,CAACC,GAAR,CAAa,UAASoC,QAAQ,CAACmF,IAAK,OAAMnF,QAAQ,CAAC+E,QAAT,CAAkBK,CAAE,KAAIpF,QAAQ,CAAC+E,QAAT,CAAkBM,CAAE,QAAO9E,cAAc,CAAC6E,CAAE,KAAI7E,cAAc,CAAC8E,CAAE,GAAnI,EAN8F,CAQ9F;;AACA7K,UAAAA,KAAK,CAACwF,QAAD,CAAL,CACKkF,KADL,CACWA,KADX,EAEKI,EAFL,CAEQT,QAFR,EAEkB;AAAEE,YAAAA,QAAQ,EAAExE;AAAZ,WAFlB,EAEgD;AAAEgF,YAAAA,MAAM,EAAE;AAAV,WAFhD,EAGKC,IAHL,CAGU,MAAM;AACR7H,YAAAA,OAAO,CAACC,GAAR,CAAa,MAAKoC,QAAQ,CAACmF,IAAK,WAAUjF,QAAQ,CAACiF,IAAK,EAAxD;AACA,iBAAKlJ,QAAL;AACA,iBAAKwJ,oBAAL,CAA0B,KAAKxJ,QAA/B;AACH,WAPL,EAQKuD,KARL;AASH;;AAEOiG,QAAAA,oBAAoB,CAACpD,KAAD,EAAgB;AACxC,cAAIA,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAKpD,eAAL,CAAqBC,MAArB,GAA8B,KAA9B;AACA,iBAAKC,cAAL,CAAoBD,MAApB,GAA6B,IAA7B;AACA,iBAAKE,YAAL,CAAkBF,MAAlB,GAA2B,KAA3B;AACA,iBAAKlC,YAAL,CAAkBkC,MAAlB,GAA2B,IAA3B;AACA,iBAAKtD,SAAL,CAAesD,MAAf,GAAwB,KAAxB;AACH;;AAED,cAAImD,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAKjG,YAAL,GAAoB,IAApB;AACA,gBAAIsJ,OAAO,GAAG,CAAd;AACA,gBAAIC,SAAS,GAAG,CACZ;AAAE9F,cAAAA,QAAQ,EAAE;AAAZ,aADY,EAEZ;AAAEA,cAAAA,QAAQ,EAAE;AAAZ,aAFY,EAGZ;AAAEA,cAAAA,QAAQ,EAAE;AAAZ,aAHY,EAIZ;AAAEA,cAAAA,QAAQ,EAAE;AAAZ,aAJY,CAAhB;;AAOA,oBAAQ,KAAK3D,SAAb;AACI,mBAAK,MAAL;AACI,qBAAKiE,eAAL,CAAqBb,YAArB,CAAkC,CAAlC,EAAqC,GAArC,EAA0C,KAA1C;AACAoG,gBAAAA,OAAO,GAAG,CAAV;AACAC,gBAAAA,SAAS,GAAG,CACR;AAAE9F,kBAAAA,QAAQ,EAAE;AAAZ,iBADQ,CAAZ;AAGA;;AACJ,mBAAK,MAAL;AACI,qBAAKM,eAAL,CAAqBb,YAArB,CAAkC,CAAlC,EAAqC,GAArC,EAA0C,KAA1C;AACAoG,gBAAAA,OAAO,GAAG,CAAV;AACAC,gBAAAA,SAAS,GAAG,CACR;AAAE9F,kBAAAA,QAAQ,EAAE;AAAZ,iBADQ,CAAZ;AAGA;;AACJ,mBAAK,MAAL;AACI,qBAAKM,eAAL,CAAqBb,YAArB,CAAkC,CAAlC,EAAqC,GAArC,EAA0C,KAA1C;AACAoG,gBAAAA,OAAO,GAAG,CAAV;AACAC,gBAAAA,SAAS,GAAG,CACR;AAAE9F,kBAAAA,QAAQ,EAAE;AAAZ,iBADQ,EAER;AAAEA,kBAAAA,QAAQ,EAAE;AAAZ,iBAFQ,CAAZ;AAIA;;AACJ,mBAAK,MAAL;AACI,qBAAKM,eAAL,CAAqBb,YAArB,CAAkC,CAAlC,EAAqC,GAArC,EAA0C,KAA1C;AACAoG,gBAAAA,OAAO,GAAG,CAAV;AACAC,gBAAAA,SAAS,GAAG,CACR;AAAE9F,kBAAAA,QAAQ,EAAE;AAAZ,iBADQ,CAAZ;AAGA;AA7BR;;AA+BA,iBAAKkD,YAAL,CAAkB,MAAM;AACpB,mBAAK3G,YAAL,GAAoB,KAApB,CADoB,CAIpB;;AACA,kBAAIuD,aAAa,GAAG,CAChB;AAAEC,gBAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,gBAAAA,QAAQ,EAAE;AAAxC,eADgB,EAEhB;AAAED,gBAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,gBAAAA,QAAQ,EAAE;AAAxC,eAFgB,EAGhB;AAAED,gBAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,gBAAAA,QAAQ,EAAE;AAAxC,eAHgB,EAIhB;AAAED,gBAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,gBAAAA,QAAQ,EAAE;AAAxC,eAJgB,CAApB;AAMA,oBAAMK,QAAQ,GAAG,KAAKC,eAAL,CAAqBrD,IAArB,CAA0BmD,cAA1B,CAAyCH,IAAI,CAACD,QAA9C,CAAjB;;AAEA,mBAAK,IAAI+F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,OAApB,EAA6BE,CAAC,EAA9B,EAAkC;AAC9B,sBAAMC,QAAQ,GAAG5L,WAAW,CAAC,KAAK6B,WAAN,CAA5B;AACA,qBAAKqB,OAAL,CAAawB,QAAb,CAAsBkH,QAAtB;AACA,sBAAMpH,QAAQ,GAAGxE,WAAW,CAAC,KAAK8B,WAAN,CAA5B;AACA,qBAAKsB,OAAL,CAAasB,QAAb,CAAsBF,QAAtB;AAEA,sBAAMqH,QAAQ,GAAG;AAAA;AAAA,wDAAgBC,OAAhB,CAAwB;AAAA;AAAA,kCAAKjI,IAAL,CAAUE,WAAV,EAAxB,EAAiD4H,CAAjD,CAAjB;;AACA,qBAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAAQ,CAAC9D,MAA7B,EAAqCgE,CAAC,EAAtC,EAA0C;AACtC,wBAAMC,QAAQ,GAAGhM,WAAW,CAAC,KAAK+B,WAAN,CAA5B;AACAyC,kBAAAA,QAAQ,CAACE,QAAT,CAAkBsH,QAAlB;AACH;AACJ;AACJ,aAzBD,EAyBG,CAzBH;AA0BH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACWhD,QAAAA,uBAAuB,CAACiD,OAAD,EAAgE;AAC1F,cAAIA,OAAO,CAAClE,MAAR,KAAmB,CAAvB,EAA0B;AACtB;AACH,WAHyF,CAK1F;;;AACA,gBAAMmE,QAAQ,GAAG,IAAIC,GAAJ,EAAjB;;AACA,eAAK,MAAMC,GAAX,IAAkBH,OAAlB,EAA2B;AACvB,gBAAIG,GAAG,GAAG,CAAN,IAAWA,GAAG,GAAG,CAArB,EAAwB;AACpB;AACH;;AACDF,YAAAA,QAAQ,CAACG,GAAT,CAAaD,GAAb,EAAkB,CAACF,QAAQ,CAACI,GAAT,CAAaF,GAAb,KAAqB,CAAtB,IAA2B,CAA7C;AACH,WAZyF,CAc1F;;;AACA,gBAAMG,MAAM,GAAGC,KAAK,CAACC,IAAN,CAAWP,QAAQ,CAACQ,MAAT,EAAX,EAA8BC,IAA9B,CAAmC,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,GAAGD,CAAjD,CAAf,CAf0F,CAiB1F;;AACA,kBAAQL,MAAM,CAACxE,MAAf;AACI,iBAAK,CAAL;AACI;AACA,qBAAO,MAAP;;AAEJ,iBAAK,CAAL;AACI,kBAAIwE,MAAM,CAAC,CAAD,CAAN,KAAc,CAAd,IAAmBA,MAAM,CAAC,CAAD,CAAN,KAAc,CAArC,EAAwC;AACpC;AACA,uBAAO,MAAP;AACH,eAHD,MAGO,IAAIA,MAAM,CAAC,CAAD,CAAN,KAAc,CAAd,IAAmBA,MAAM,CAAC,CAAD,CAAN,KAAc,CAArC,EAAwC;AAC3C;AACA,uBAAO,MAAP;AACH;;AACD;;AAEJ,iBAAK,CAAL;AACI;AACA,kBAAIA,MAAM,CAAC,CAAD,CAAN,KAAc,CAAd,IAAmBA,MAAM,CAAC,CAAD,CAAN,KAAc,CAAjC,IAAsCA,MAAM,CAAC,CAAD,CAAN,KAAc,CAAxD,EAA2D;AACvD,uBAAO,MAAP;AACH;;AACD;;AAEJ,iBAAK,CAAL;AACI;AACA,qBAAO,MAAP;AAxBR;;AA0BA,iBAAO,IAAP;AACH;;AAnbqC,O;;;;;iBAGb,I;;;;;;;iBAEI,I;;;;;;;iBAEH,I;;;;;;;iBAEC,I;;;;;;;iBAGK,I;;;;;;;iBAED,I;;;;;;;iBAEF,I;;;;;;;iBAEA,I;;;;;;;iBAGF,I;;;;;;;iBAEY,I;;;;;;;iBAEf,I;;;;;;;iBAEA,I;;;;;;;iBAGO,I", "sourcesContent": ["import { _decorator, Component, instantiate, Label, Node, Prefab, sp, Sprite, SpriteFrame, tween, UITransform, Vec3, v3 } from 'cc';\r\nimport { lobby, yy } from 'yycore';\r\nimport { oops } from '../../core/Oop';\r\nimport { ELIXIR_MAP_LIST, ITEM } from '../../common/GameUtils';\r\nimport { itemcfg } from '../../game/model/ItemCfg';\r\nimport { ElixirViewItem } from './ElixirViewItem';\r\nimport { DigTreasure } from '../../proto/DigTreasureProtocol_client';\r\nimport { Message } from '../../event/MessageManager';\r\nimport { resLoader } from '../../loader/ResLoader';\r\nimport { config } from '../../common/Config';\r\nimport { elixirUnsealCfg } from '../../game/model/ElixirUnsealCfg';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('ElixirView')\r\nexport class ElixirView extends Component {\r\n\r\n    @property(Prefab)\r\n    private pbItem: Prefab = null;\r\n    @property(Node)\r\n    private ndElixirArea: Node = null;\r\n    @property(Label)\r\n    private labTimer: Label = null;\r\n    @property(Label)\r\n    private labLingqi: Label = null;\r\n\r\n    @property(Node)\r\n    private ndBeforeKaiDing: Node = null;\r\n    @property(Node)\r\n    private ndAfterKaiDing: Node = null;\r\n    @property(Node)\r\n    private ndZiYangArea: Node = null;\r\n    @property(Node)\r\n    private ndResultArea: Node = null;\r\n\r\n    @property(Node)\r\n    private ndOpenArea: Node = null;\r\n    @property(sp.Skeleton)\r\n    private skOpenBlankArea: sp.Skeleton = null;\r\n    @property(Node)\r\n    private ndLines: Node = null;\r\n    @property(Node)\r\n    private ndItems: Node = null;\r\n\r\n    @property(sp.Skeleton)\r\n    private skeLuzi: sp.Skeleton = null;\r\n\r\n    private _kaidingData: DigTreasure.PtNextKaiDingSubInfo = null;\r\n\r\n    private _luziNoot: Node = null;\r\n\r\n    private _resultPrefab: Node = null;\r\n    private _linePrefab: Node = null;\r\n    private _itemPrefab: Node = null;\r\n    private _giftPrefab: Node = null;\r\n\r\n    private _moveNum: number = 0;\r\n    private _openType: 'ABCD' | 'AABC' | 'AAAB' | 'AAAA' | 'AABB' = null;\r\n\r\n    private _openPosList: Vec3[] = [];\r\n    private _isSyncBlank = false;\r\n\r\n    onLoad() {\r\n        Message.on(\"on_elixir_auto_kaiding_begin\", this.onKaidingBegin, this);\r\n        Message.on(\"on_elixir_auto_kaiding_finish\", this.onKaidingFinish, this);\r\n        Message.on(\"on_elixir_once_kaiding_begin\", this.onKaidingBegin, this);\r\n        Message.on(\"on_elixir_once_kaiding_finish\", this.onKaidingFinish, this);\r\n\r\n        yy.event.on(this, lobby.events.MyPropsChanged, this.onUpdateProps);\r\n\r\n        this._luziNoot = this.skeLuzi.node.parent;\r\n\r\n        this._resultPrefab = instantiate(this.ndResultArea.children[0]);\r\n        this.ndResultArea.destroyAllChildren();\r\n\r\n        this._linePrefab = instantiate(this.ndLines.children[0]);\r\n        this.ndLines.destroyAllChildren();\r\n        const ndItem = this.ndItems.children[0];\r\n        const ndGift = ndItem.children[0];\r\n        this._giftPrefab = instantiate(ndGift);\r\n        ndItem.destroyAllChildren();\r\n        this._itemPrefab = instantiate(ndItem);\r\n        this.ndItems.destroyAllChildren();\r\n    }\r\n\r\n    onDestroy() {\r\n        Message.off(\"on_elixir_auto_kaiding_begin\", this.onKaidingBegin, this);\r\n        Message.off(\"on_elixir_auto_kaiding_finish\", this.onKaidingFinish, this);\r\n        Message.off(\"on_elixir_once_kaiding_begin\", this.onKaidingBegin, this);\r\n        Message.off(\"on_elixir_once_kaiding_finish\", this.onKaidingFinish, this);\r\n\r\n        yy.event.off(lobby.events.MyPropsChanged, this.onUpdateProps);\r\n    }\r\n\r\n    onAdded(data) {\r\n        console.log('ElixirView -- onAdded', data);\r\n        const kaidingData = oops.game.getNextAutoKaiDingInfo(oops.game.getEMapType());\r\n        this._kaidingData = kaidingData;\r\n\r\n        this.ndElixirArea.destroyAllChildren();\r\n\r\n        const lMapType = oops.game.getLMapType();\r\n        const itemIdList = ELIXIR_MAP_LIST[lMapType];\r\n        itemIdList.forEach(itemId => {\r\n            const itemDataCfg = itemcfg.getDataByIndex(itemId);\r\n            const itemNode = instantiate(this.pbItem);\r\n            this.ndElixirArea.addChild(itemNode);\r\n\r\n            const itemComp = itemNode.getComponent(ElixirViewItem);\r\n            itemComp.init(itemDataCfg);\r\n        });\r\n\r\n        this.updateTimer();\r\n        this.updateLingqi();\r\n\r\n        this.ndBeforeKaiDing.active = true;\r\n        this.ndAfterKaiDing.active = false;\r\n        this.ndZiYangArea.active = true;\r\n        this.ndResultArea.active = false;\r\n        this.ndOpenArea.active = false;\r\n        this._luziNoot.active = true;\r\n        this.skeLuzi.setAnimation(0, \"loop\", true);\r\n        this._moveNum = 0;\r\n        this._openType = null;\r\n        this._isSyncBlank = false;\r\n    }\r\n\r\n    onEnable(): void {\r\n        console.log('ElixirView -- onEnable');\r\n    }\r\n\r\n    start() {\r\n        console.log('ElixirView -- start');\r\n    }\r\n\r\n    update(dt: number) {\r\n        this.updateTimer();\r\n\r\n        if (this._isSyncBlank) {\r\n            // 定义小球和坑位的对应关系\r\n            const ballSlotPairs = [\r\n                { ballName: \"icon_elixir_01\", slotName: \"icon_elixir_bg_1\" },\r\n                { ballName: \"icon_elixir_02\", slotName: \"icon_elixir_bg_2\" },\r\n                { ballName: \"icon_elixir_03\", slotName: \"icon_elixir_bg_3\" },\r\n                { ballName: \"icon_elixir_04\", slotName: \"icon_elixir_bg_4\" }\r\n            ];\r\n\r\n            // 遍历每个小球和对应的坑位\r\n            ballSlotPairs.forEach((pair, index) => {\r\n                const ballNode = this.ndOpenArea.getChildByName(pair.ballName);\r\n                const slotNode = this.skOpenBlankArea.node.getChildByName(pair.slotName);\r\n                const slotWorldPos = slotNode.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO);\r\n                const targetLocalPos = ballNode.parent!.getComponent(UITransform)!.convertToNodeSpaceAR(slotWorldPos);\r\n                ballNode.setPosition(targetLocalPos);\r\n            });\r\n        }\r\n    }\r\n\r\n    protected onCloseClick() {\r\n        oops.gui.removeByNode(this.node)\r\n    }\r\n\r\n    protected onBtnManulKaiDing() {\r\n        oops.game.m_gameNet.Send_ReqOnceKaiDing();\r\n    }\r\n\r\n    protected onBtnUseAll() {\r\n        console.debug('onBtnUseAll');\r\n        // 测试移动小球功能\r\n        this.moveElixirBallsToSlots();\r\n    }\r\n\r\n    private updateTimer() {\r\n        if (this._kaidingData) {\r\n            const curTime = Date.now() + oops.game.getServerTimeOffset();\r\n            const timeData = Math.floor((this._kaidingData.nextKaiDingTime.toNumber() * 1000 - curTime) / 1000);\r\n            const minutes: number = Math.floor(timeData / 60);\r\n            const remainingSeconds: number = timeData % 60;\r\n            const formattedMinutes: string = minutes.toString().length < 2 ? `0${minutes.toString()}` : minutes.toString();\r\n            const formattedSeconds: string = remainingSeconds.toString().length < 2 ? `0${remainingSeconds.toString()}` : remainingSeconds.toString();\r\n            this.labTimer.string = \"开鼎时间 \" + (minutes <= 0 && remainingSeconds <= 0 ? \"00:00\" : `${formattedMinutes}:${formattedSeconds}`);\r\n        }\r\n    }\r\n\r\n    private updateLingqi() {\r\n        const eMapType = oops.game.getEMapType();\r\n        let count = 0;\r\n        switch (eMapType) {\r\n            case DigTreasure.eMapType.eMT_Shan2:\r\n                count = lobby.myBag.getPropCountByID(ITEM.LINGQI_1) | 0;\r\n                break;\r\n            case DigTreasure.eMapType.eMT_Dong2:\r\n                count = lobby.myBag.getPropCountByID(ITEM.LINGQI_2) | 0;\r\n                break;\r\n            case DigTreasure.eMapType.eMT_Ling2:\r\n                count = lobby.myBag.getPropCountByID(ITEM.LINGQI_3) | 0;\r\n                break;\r\n        }\r\n        this.labLingqi.string = count.toString();\r\n    }\r\n\r\n    private onKaidingBegin(event, data: { batId: string, reclaimXianDanList: DigTreasure.PtGiftAList, bool?: boolean }) {\r\n        // this.ndBeforeKaiDing.active = false;\r\n        // this.ndAfterKaiDing.active = true;\r\n        // this.ndZiYangArea.active = false;\r\n        // this.ndResultArea.active = true;\r\n        // this.ndResultArea.destroyAllChildren();\r\n\r\n        this.skeLuzi.setAnimation(0, \"kai\", false);\r\n        this.scheduleOnce(() => {\r\n            this.ndOpenArea.active = true;\r\n            this.moveElixirBallsToSlots();\r\n        }, 1.2);\r\n    }\r\n\r\n    private onKaidingFinish(event, data: { kaidingPlan: DigTreasure.PtAutoKaiDingPlan, kaidingMingxi: DigTreasure.PtPlayerKaiDingMingXiA }) {\r\n        const elixirList = ELIXIR_MAP_LIST[oops.game.getLMapType()];\r\n\r\n        this._openType = this.analyzeArrayCombination(data.kaidingPlan.kaiDingResults);\r\n        console.log(\"开鼎结果组合类型:\", this._openType);\r\n        data.kaidingPlan.kaiDingResults.forEach((result, index) => {\r\n            const elixirId = elixirList[result];\r\n            const sp = this.ndOpenArea.children[index].getComponent(Sprite);\r\n            const itemDataCfg = itemcfg.getDataByIndex(elixirId);\r\n            this.loadSp(`icon/elixir/${itemDataCfg.res}/spriteFrame`, sp);\r\n        });\r\n\r\n        this.ndResultArea.destroyAllChildren();\r\n        data.kaidingPlan.zhanWeiGiftAList.forEach((gift, index) => {\r\n            const elixirId = elixirList[index];\r\n            const costCount = data.kaidingPlan.xianDanCostCounts[index] || 0;\r\n            this.loadResultItem(elixirId, costCount, gift);\r\n        });\r\n    }\r\n\r\n    private loadSp(iconPath: string, spriteNode: Sprite) {\r\n        resLoader.load(config.game.bundleName, iconPath, SpriteFrame, (err, spFrame: SpriteFrame) => {\r\n            if (err) {\r\n                return\r\n            }\r\n            spriteNode.spriteFrame = spFrame;\r\n        });\r\n    }\r\n\r\n    private loadResultItem(elixirId: number, count: number, gift: DigTreasure.PtZhanWeiGiftA) {\r\n        const ndItem = instantiate(this._resultPrefab);\r\n        this.ndResultArea.addChild(ndItem);\r\n\r\n        const elixirIcon = ndItem.getChildByName(\"elixir_icon\").getComponent(Sprite);\r\n        const elixirCount = ndItem.getChildByName(\"elixir_count\").getComponent(Label);\r\n        const elixirDataCfg = itemcfg.getDataByIndex(elixirId);\r\n        this.loadSp(`icon/elixir/${elixirDataCfg.res}/spriteFrame`, elixirIcon);\r\n        elixirCount.string = `${count}`;\r\n\r\n        const giftIcon = ndItem.getChildByName(\"gift_icon\").getComponent(Sprite);\r\n        const giftCount = ndItem.getChildByName(\"gift_count\").getComponent(Label);\r\n        const giftDataCfg = itemcfg.getDataByIndex(parseInt(gift.giftId));\r\n        this.loadSp(`icon/item/${giftDataCfg.res}/spriteFrame`, giftIcon);\r\n        giftCount.string = `${gift.giftNum}`;\r\n    }\r\n\r\n    private onUpdateProps() {\r\n        this.updateLingqi();\r\n    }\r\n\r\n    /**\r\n     * 移动红框中的4个小球到黄框中对应的坑位\r\n     * 从 OpenNode 下的 icon_elixir_01-04 移动到 OpenBox 下的 icon_elixir_bg_1-4\r\n     * @param duration 动画持续时间，默认0.8秒\r\n     * @param delayBetween 每个小球之间的延迟时间，默认0.1秒\r\n     */\r\n    public moveElixirBallsToSlots(duration: number = 0.8, delayBetween: number = 0.1) {\r\n        console.log(\"找到 OpenNode 和 OpenBox 节点，开始移动小球\");\r\n\r\n        // 定义小球和坑位的对应关系\r\n        const ballSlotPairs = [\r\n            { ballName: \"icon_elixir_01\", slotName: \"icon_elixir_bg_1\" },\r\n            { ballName: \"icon_elixir_02\", slotName: \"icon_elixir_bg_2\" },\r\n            { ballName: \"icon_elixir_03\", slotName: \"icon_elixir_bg_3\" },\r\n            { ballName: \"icon_elixir_04\", slotName: \"icon_elixir_bg_4\" }\r\n        ];\r\n\r\n        // 遍历每个小球和对应的坑位\r\n        ballSlotPairs.forEach((pair, index) => {\r\n            const ballNode = this.ndOpenArea.getChildByName(pair.ballName);\r\n            if (this._openPosList[index]) {\r\n                ballNode.setPosition(this._openPosList[index]);\r\n            }\r\n            const slotNode = this.skOpenBlankArea.node.getChildByName(pair.slotName);\r\n            this._openPosList[index] = ballNode.position.clone();\r\n            this.moveBallToSlot(ballNode, slotNode, index * delayBetween, duration);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 移动单个小球到指定坑位\r\n     * @param ballNode 小球节点\r\n     * @param slotNode 坑位节点\r\n     * @param delay 延迟时间\r\n     * @param duration 动画持续时间\r\n     */\r\n    private moveBallToSlot(ballNode: Node, slotNode: Node, delay: number = 0, duration: number = 0.8) {\r\n        // 获取坑位在世界坐标系中的位置\r\n        const slotWorldPos = slotNode.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO);\r\n        // 将坑位的世界坐标转换为小球父节点的本地坐标\r\n        const targetLocalPos = ballNode.parent!.getComponent(UITransform)!.convertToNodeSpaceAR(slotWorldPos);\r\n\r\n        console.log(`准备移动小球 ${ballNode.name} 从 (${ballNode.position.x}, ${ballNode.position.y}) 到 (${targetLocalPos.x}, ${targetLocalPos.y})`);\r\n\r\n        // 使用 tween 动画移动小球\r\n        tween(ballNode)\r\n            .delay(delay)\r\n            .to(duration, { position: targetLocalPos }, { easing: 'cubicOut' })\r\n            .call(() => {\r\n                console.log(`小球 ${ballNode.name} 已移动到坑位 ${slotNode.name}`);\r\n                this._moveNum++;\r\n                this.switchToAfterKaiDing(this._moveNum);\r\n            })\r\n            .start();\r\n    }\r\n\r\n    private switchToAfterKaiDing(count: number) {\r\n        if (count == 1) {\r\n            this.ndBeforeKaiDing.active = false;\r\n            this.ndAfterKaiDing.active = true;\r\n            this.ndZiYangArea.active = false;\r\n            this.ndResultArea.active = true;\r\n            this._luziNoot.active = false;\r\n        }\r\n\r\n        if (count == 4) {\r\n            this._isSyncBlank = true;\r\n            let openNum = 4;\r\n            let slotPairs = [\r\n                { slotName: \"icon_elixir_01/skeleton\" },\r\n                { slotName: \"icon_elixir_02/skeleton\" },\r\n                { slotName: \"icon_elixir_03/skeleton\" },\r\n                { slotName: \"icon_elixir_04/skeleton\" }\r\n            ];\r\n\r\n            switch (this._openType) {\r\n                case 'AAAA':\r\n                    this.skOpenBlankArea.setAnimation(0, \"4\", false);\r\n                    openNum = 1;\r\n                    slotPairs = [\r\n                        { slotName: \"icon_elixir_04/skeleton\" }\r\n                    ];\r\n                    break;\r\n                case 'AAAB':\r\n                    this.skOpenBlankArea.setAnimation(0, \"3\", false);\r\n                    openNum = 2;\r\n                    slotPairs = [\r\n                        { slotName: \"icon_elixir_03/skeleton\" }\r\n                    ];\r\n                    break;\r\n                case 'AABB':\r\n                    this.skOpenBlankArea.setAnimation(0, \"1\", false);\r\n                    openNum = 2;\r\n                    slotPairs = [\r\n                        { slotName: \"icon_elixir_02/skeleton\" },\r\n                        { slotName: \"icon_elixir_04/skeleton\" }\r\n                    ];\r\n                    break;\r\n                case 'AABC':\r\n                    this.skOpenBlankArea.setAnimation(0, \"2\", false);\r\n                    openNum = 3;\r\n                    slotPairs = [\r\n                        { slotName: \"icon_elixir_02/skeleton\" }\r\n                    ];\r\n                    break;\r\n            }\r\n            this.scheduleOnce(() => {\r\n                this._isSyncBlank = false;\r\n\r\n\r\n                // 定义小球和坑位的对应关系\r\n                let ballSlotPairs = [\r\n                    { ballName: \"icon_elixir_01\", slotName: \"icon_elixir_bg_1\" },\r\n                    { ballName: \"icon_elixir_02\", slotName: \"icon_elixir_bg_2\" },\r\n                    { ballName: \"icon_elixir_03\", slotName: \"icon_elixir_bg_3\" },\r\n                    { ballName: \"icon_elixir_04\", slotName: \"icon_elixir_bg_4\" }\r\n                ];\r\n                const slotNode = this.skOpenBlankArea.node.getChildByName(pair.slotName);\r\n\r\n                for (let i = 0; i < openNum; i++) {\r\n                    const lineNode = instantiate(this._linePrefab);\r\n                    this.ndLines.addChild(lineNode);\r\n                    const itemNode = instantiate(this._itemPrefab);\r\n                    this.ndItems.addChild(itemNode);\r\n\r\n                    const dataList = elixirUnsealCfg.getData(oops.game.getEMapType(), i);\r\n                    for (let j = 0; j < dataList.length; j++) {\r\n                        const giftNode = instantiate(this._giftPrefab);\r\n                        itemNode.addChild(giftNode);\r\n                    }\r\n                }\r\n            }, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 分析数组组合类型\r\n     * @param numbers 长度为4的数字数组，每个元素范围0-7\r\n     * @returns 组合类型字符串：'ABCD' | 'AABC' | 'AAAB' | 'AAAA' | 'AABB'\r\n     */\r\n    public analyzeArrayCombination(numbers: number[]): 'ABCD' | 'AABC' | 'AAAB' | 'AAAA' | 'AABB' {\r\n        if (numbers.length !== 4) {\r\n            return;\r\n        }\r\n\r\n        // 统计每个数字出现的次数\r\n        const countMap = new Map<number, number>();\r\n        for (const num of numbers) {\r\n            if (num < 0 || num > 7) {\r\n                return;\r\n            }\r\n            countMap.set(num, (countMap.get(num) || 0) + 1);\r\n        }\r\n\r\n        // 获取出现次数的数组并排序\r\n        const counts = Array.from(countMap.values()).sort((a, b) => b - a);\r\n\r\n        // 根据出现次数的模式判断组合类型\r\n        switch (counts.length) {\r\n            case 1:\r\n                // 只有一种数字，出现4次 -> AAAA\r\n                return 'AAAA';\r\n\r\n            case 2:\r\n                if (counts[0] === 3 && counts[1] === 1) {\r\n                    // 一种数字出现3次，另一种出现1次 -> AAAB\r\n                    return 'AAAB';\r\n                } else if (counts[0] === 2 && counts[1] === 2) {\r\n                    // 两种数字各出现2次 -> AABB\r\n                    return 'AABB';\r\n                }\r\n                break;\r\n\r\n            case 3:\r\n                // 三种不同数字，其中一种出现2次，另外两种各出现1次 -> AABC\r\n                if (counts[0] === 2 && counts[1] === 1 && counts[2] === 1) {\r\n                    return 'AABC';\r\n                }\r\n                break;\r\n\r\n            case 4:\r\n                // 四种不同数字，各出现1次 -> ABCD\r\n                return 'ABCD';\r\n        }\r\n        return null;\r\n    }\r\n\r\n}\r\n\r\n\r\n"]}