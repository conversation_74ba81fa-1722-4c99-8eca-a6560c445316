{"version": 3, "sources": ["file:///F:/SVN/plinko/BasePro/DemoMoveV3/assets/scripts/ui/elixir/ElixirView.ts"], "names": ["_decorator", "Component", "instantiate", "Label", "Node", "Prefab", "sp", "Sprite", "SpriteFrame", "tween", "UITransform", "Vec3", "lobby", "yy", "oops", "ELIXIR_MAP_LIST", "ITEM", "itemcfg", "ElixirViewItem", "DigTreasure", "Message", "re<PERSON><PERSON><PERSON><PERSON>", "config", "elixirUnsealCfg", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Skeleton", "_kaidingData", "_luziNoot", "_resultPrefab", "_linePrefab", "_itemPrefab", "_giftPrefab", "_moveNum", "onLoad", "on", "onKaidingBegin", "onKaidingFinish", "event", "events", "MyPropsChanged", "onUpdateProps", "skeLuzi", "node", "parent", "ndResultArea", "children", "destroyAllChildren", "ndLines", "ndItem", "ndItems", "ndGift", "onDestroy", "off", "onAdded", "data", "kaidingData", "game", "getNextAutoKaiDingInfo", "getEMapType", "ndElixirArea", "lMapType", "getLMapType", "itemIdList", "for<PERSON>ach", "itemId", "itemDataCfg", "getDataByIndex", "itemNode", "pbItem", "<PERSON><PERSON><PERSON><PERSON>", "itemComp", "getComponent", "init", "updateTimer", "updateLingqi", "ndBeforeKaiDing", "active", "ndAfterKaiDing", "ndZiYangArea", "ndOpenArea", "start", "update", "dt", "onCloseClick", "gui", "removeByNode", "onBtnManulKaiDing", "m_gameNet", "Send_ReqOnceKaiDing", "onBtnUseAll", "console", "debug", "moveElixirBallsToSlots", "curTime", "Date", "now", "getServerTimeOffset", "timeData", "Math", "floor", "nextKaiDingTime", "toNumber", "minutes", "remainingSeconds", "formattedMinutes", "toString", "length", "formattedSeconds", "labTimer", "string", "eMapType", "count", "eMT_Shan2", "myBag", "getPropCountByID", "LINGQI_1", "eMT_Dong2", "LINGQI_2", "eMT_Ling2", "LINGQI_3", "labLingqi", "setAnimation", "scheduleOnce", "elixirList", "kaidingPlan", "kaiDingResults", "result", "index", "elixirId", "loadSp", "res", "zhanWeiGiftAList", "gift", "costCount", "xianDanCostCounts", "loadResultItem", "iconPath", "spriteNode", "load", "bundleName", "err", "spFrame", "spriteFrame", "elixirIcon", "getChildByName", "elixirCount", "elixirDataCfg", "giftIcon", "giftCount", "giftDataCfg", "parseInt", "giftId", "giftNum", "duration", "delayBetween", "log", "ballSlotPairs", "ballName", "slotName", "pair", "ballNode", "slotNode", "ndOpenBlankArea", "moveBallToSlot", "delay", "slotWorldPos", "convertToWorldSpaceAR", "ZERO", "targetLocalPos", "convertToNodeSpaceAR", "name", "position", "x", "y", "to", "easing", "call", "switchToAfterKaiDing", "i", "lineNode", "dataList", "getData", "j", "giftNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AACtGC,MAAAA,K,WAAAA,K;AAAOC,MAAAA,E,WAAAA,E;;AACPC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,e,iBAAAA,e;AAAiBC,MAAAA,I,iBAAAA,I;;AACjBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,M,iBAAAA,M;;AAEAC,MAAAA,e,kBAAAA,e;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBzB,U;;4BAGjB0B,U,WADZF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACpB,MAAD,C,UAERoB,QAAQ,CAACrB,IAAD,C,UAERqB,QAAQ,CAACtB,KAAD,C,UAERsB,QAAQ,CAACtB,KAAD,C,UAGRsB,QAAQ,CAACrB,IAAD,C,UAERqB,QAAQ,CAACrB,IAAD,C,UAERqB,QAAQ,CAACrB,IAAD,C,UAERqB,QAAQ,CAACrB,IAAD,C,WAGRqB,QAAQ,CAACrB,IAAD,C,WAERqB,QAAQ,CAACrB,IAAD,C,WAERqB,QAAQ,CAACrB,IAAD,C,WAERqB,QAAQ,CAACrB,IAAD,C,WAGRqB,QAAQ,CAACnB,EAAE,CAACqB,QAAJ,C,2BA9Bb,MACaD,UADb,SACgCzB,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAgC9B2B,YAhC8B,GAgCmB,IAhCnB;AAAA,eAkC9BC,SAlC8B,GAkCZ,IAlCY;AAAA,eAoC9BC,aApC8B,GAoCR,IApCQ;AAAA,eAqC9BC,WArC8B,GAqCV,IArCU;AAAA,eAsC9BC,WAtC8B,GAsCV,IAtCU;AAAA,eAuC9BC,WAvC8B,GAuCV,IAvCU;AAAA,eAyC9BC,QAzC8B,GAyCX,CAzCW;AAAA;;AA2CtCC,QAAAA,MAAM,GAAG;AACL;AAAA;AAAA,kCAAQC,EAAR,CAAW,8BAAX,EAA2C,KAAKC,cAAhD,EAAgE,IAAhE;AACA;AAAA;AAAA,kCAAQD,EAAR,CAAW,+BAAX,EAA4C,KAAKE,eAAjD,EAAkE,IAAlE;AACA;AAAA;AAAA,kCAAQF,EAAR,CAAW,8BAAX,EAA2C,KAAKC,cAAhD,EAAgE,IAAhE;AACA;AAAA;AAAA,kCAAQD,EAAR,CAAW,+BAAX,EAA4C,KAAKE,eAAjD,EAAkE,IAAlE;AAEA;AAAA;AAAA,wBAAGC,KAAH,CAASH,EAAT,CAAY,IAAZ,EAAkB;AAAA;AAAA,8BAAMI,MAAN,CAAaC,cAA/B,EAA+C,KAAKC,aAApD;AAEA,eAAKb,SAAL,GAAiB,KAAKc,OAAL,CAAaC,IAAb,CAAkBC,MAAnC;AAEA,eAAKf,aAAL,GAAqB5B,WAAW,CAAC,KAAK4C,YAAL,CAAkBC,QAAlB,CAA2B,CAA3B,CAAD,CAAhC;AACA,eAAKD,YAAL,CAAkBE,kBAAlB;AAEA,eAAKjB,WAAL,GAAmB7B,WAAW,CAAC,KAAK+C,OAAL,CAAaF,QAAb,CAAsB,CAAtB,CAAD,CAA9B;AACA,eAAKE,OAAL,CAAaD,kBAAb;AACA,gBAAME,MAAM,GAAG,KAAKC,OAAL,CAAaJ,QAAb,CAAsB,CAAtB,CAAf;AACA,gBAAMK,MAAM,GAAGF,MAAM,CAACH,QAAP,CAAgB,CAAhB,CAAf;AACA,eAAKd,WAAL,GAAmB/B,WAAW,CAACkD,MAAD,CAA9B;AACAF,UAAAA,MAAM,CAACF,kBAAP;AACA,eAAKhB,WAAL,GAAmB9B,WAAW,CAACgD,MAAD,CAA9B;AACA,eAAKC,OAAL,CAAaH,kBAAb;AACH;;AAEDK,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQC,GAAR,CAAY,8BAAZ,EAA4C,KAAKjB,cAAjD,EAAiE,IAAjE;AACA;AAAA;AAAA,kCAAQiB,GAAR,CAAY,+BAAZ,EAA6C,KAAKhB,eAAlD,EAAmE,IAAnE;AACA;AAAA;AAAA,kCAAQgB,GAAR,CAAY,8BAAZ,EAA4C,KAAKjB,cAAjD,EAAiE,IAAjE;AACA;AAAA;AAAA,kCAAQiB,GAAR,CAAY,+BAAZ,EAA6C,KAAKhB,eAAlD,EAAmE,IAAnE;AAEA;AAAA;AAAA,wBAAGC,KAAH,CAASe,GAAT,CAAa;AAAA;AAAA,8BAAMd,MAAN,CAAaC,cAA1B,EAA0C,KAAKC,aAA/C;AACH;;AAEDa,QAAAA,OAAO,CAACC,IAAD,EAAO;AACV,gBAAMC,WAAW,GAAG;AAAA;AAAA,4BAAKC,IAAL,CAAUC,sBAAV,CAAiC;AAAA;AAAA,4BAAKD,IAAL,CAAUE,WAAV,EAAjC,CAApB;AACA,eAAKhC,YAAL,GAAoB6B,WAApB;AAEA,eAAKI,YAAL,CAAkBb,kBAAlB;AAEA,gBAAMc,QAAQ,GAAG;AAAA;AAAA,4BAAKJ,IAAL,CAAUK,WAAV,EAAjB;AACA,gBAAMC,UAAU,GAAG;AAAA;AAAA,kDAAgBF,QAAhB,CAAnB;AACAE,UAAAA,UAAU,CAACC,OAAX,CAAmBC,MAAM,IAAI;AACzB,kBAAMC,WAAW,GAAG;AAAA;AAAA,oCAAQC,cAAR,CAAuBF,MAAvB,CAApB;AACA,kBAAMG,QAAQ,GAAGnE,WAAW,CAAC,KAAKoE,MAAN,CAA5B;AACA,iBAAKT,YAAL,CAAkBU,QAAlB,CAA2BF,QAA3B;AAEA,kBAAMG,QAAQ,GAAGH,QAAQ,CAACI,YAAT;AAAA;AAAA,iDAAjB;AACAD,YAAAA,QAAQ,CAACE,IAAT,CAAcP,WAAd;AACH,WAPD;AASA,eAAKQ,WAAL;AACA,eAAKC,YAAL;AAEA,eAAKC,eAAL,CAAqBC,MAArB,GAA8B,IAA9B;AACA,eAAKC,cAAL,CAAoBD,MAApB,GAA6B,KAA7B;AACA,eAAKE,YAAL,CAAkBF,MAAlB,GAA2B,IAA3B;AACA,eAAKhC,YAAL,CAAkBgC,MAAlB,GAA2B,KAA3B;AACA,eAAKG,UAAL,CAAgBH,MAAhB,GAAyB,KAAzB;AACH;;AAEDI,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,eAAKT,WAAL;AACH;;AAESU,QAAAA,YAAY,GAAG;AACrB;AAAA;AAAA,4BAAKC,GAAL,CAASC,YAAT,CAAsB,KAAK3C,IAA3B;AACH;;AAES4C,QAAAA,iBAAiB,GAAG;AAC1B;AAAA;AAAA,4BAAK9B,IAAL,CAAU+B,SAAV,CAAoBC,mBAApB;AACH;;AAESC,QAAAA,WAAW,GAAG;AACpBC,UAAAA,OAAO,CAACC,KAAR,CAAc,aAAd,EADoB,CAEpB;;AACA,eAAKC,sBAAL;AACH;;AAEOnB,QAAAA,WAAW,GAAG;AAClB,cAAI,KAAK/C,YAAT,EAAuB;AACnB,kBAAMmE,OAAO,GAAGC,IAAI,CAACC,GAAL,KAAa;AAAA;AAAA,8BAAKvC,IAAL,CAAUwC,mBAAV,EAA7B;AACA,kBAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAL,CAAW,CAAC,KAAKzE,YAAL,CAAkB0E,eAAlB,CAAkCC,QAAlC,KAA+C,IAA/C,GAAsDR,OAAvD,IAAkE,IAA7E,CAAjB;AACA,kBAAMS,OAAe,GAAGJ,IAAI,CAACC,KAAL,CAAWF,QAAQ,GAAG,EAAtB,CAAxB;AACA,kBAAMM,gBAAwB,GAAGN,QAAQ,GAAG,EAA5C;AACA,kBAAMO,gBAAwB,GAAGF,OAAO,CAACG,QAAR,GAAmBC,MAAnB,GAA4B,CAA5B,GAAiC,IAAGJ,OAAO,CAACG,QAAR,EAAmB,EAAvD,GAA2DH,OAAO,CAACG,QAAR,EAA5F;AACA,kBAAME,gBAAwB,GAAGJ,gBAAgB,CAACE,QAAjB,GAA4BC,MAA5B,GAAqC,CAArC,GAA0C,IAAGH,gBAAgB,CAACE,QAAjB,EAA4B,EAAzE,GAA6EF,gBAAgB,CAACE,QAAjB,EAA9G;AACA,iBAAKG,QAAL,CAAcC,MAAd,GAAuB,WAAWP,OAAO,IAAI,CAAX,IAAgBC,gBAAgB,IAAI,CAApC,GAAwC,OAAxC,GAAmD,GAAEC,gBAAiB,IAAGG,gBAAiB,EAArG,CAAvB;AACH;AACJ;;AAEOjC,QAAAA,YAAY,GAAG;AACnB,gBAAMoC,QAAQ,GAAG;AAAA;AAAA,4BAAKtD,IAAL,CAAUE,WAAV,EAAjB;AACA,cAAIqD,KAAK,GAAG,CAAZ;;AACA,kBAAQD,QAAR;AACI,iBAAK;AAAA;AAAA,4CAAYA,QAAZ,CAAqBE,SAA1B;AACID,cAAAA,KAAK,GAAG;AAAA;AAAA,kCAAME,KAAN,CAAYC,gBAAZ,CAA6B;AAAA;AAAA,gCAAKC,QAAlC,IAA8C,CAAtD;AACA;;AACJ,iBAAK;AAAA;AAAA,4CAAYL,QAAZ,CAAqBM,SAA1B;AACIL,cAAAA,KAAK,GAAG;AAAA;AAAA,kCAAME,KAAN,CAAYC,gBAAZ,CAA6B;AAAA;AAAA,gCAAKG,QAAlC,IAA8C,CAAtD;AACA;;AACJ,iBAAK;AAAA;AAAA,4CAAYP,QAAZ,CAAqBQ,SAA1B;AACIP,cAAAA,KAAK,GAAG;AAAA;AAAA,kCAAME,KAAN,CAAYC,gBAAZ,CAA6B;AAAA;AAAA,gCAAKK,QAAlC,IAA8C,CAAtD;AACA;AATR;;AAWA,eAAKC,SAAL,CAAeX,MAAf,GAAwBE,KAAK,CAACN,QAAN,EAAxB;AACH;;AAEOtE,QAAAA,cAAc,CAACE,KAAD,EAAQiB,IAAR,EAA8F;AAChH;AACA;AACA;AACA;AACA;AAEA,eAAKb,OAAL,CAAagF,YAAb,CAA0B,CAA1B,EAA6B,KAA7B,EAAoC,KAApC;AACA,eAAKC,YAAL,CAAkB,MAAM;AACpB,iBAAK3C,UAAL,CAAgBH,MAAhB,GAAyB,IAAzB;AACA,iBAAKgB,sBAAL;AACH,WAHD,EAGG,GAHH;AAIH;;AAEOxD,QAAAA,eAAe,CAACC,KAAD,EAAQiB,IAAR,EAAiH;AACpI,gBAAMqE,UAAU,GAAG;AAAA;AAAA,kDAAgB;AAAA;AAAA,4BAAKnE,IAAL,CAAUK,WAAV,EAAhB,CAAnB;AAEAP,UAAAA,IAAI,CAACsE,WAAL,CAAiBC,cAAjB,CAAgC9D,OAAhC,CAAwC,CAAC+D,MAAD,EAASC,KAAT,KAAmB;AACvD,kBAAMC,QAAQ,GAAGL,UAAU,CAACG,MAAD,CAA3B;AACA,kBAAM1H,EAAE,GAAG,KAAK2E,UAAL,CAAgBlC,QAAhB,CAAyBkF,KAAzB,EAAgCxD,YAAhC,CAA6ClE,MAA7C,CAAX;AACA,kBAAM4D,WAAW,GAAG;AAAA;AAAA,oCAAQC,cAAR,CAAuB8D,QAAvB,CAApB;AACA,iBAAKC,MAAL,CAAa,eAAchE,WAAW,CAACiE,GAAI,cAA3C,EAA0D9H,EAA1D;AACH,WALD;AAOA,eAAKwC,YAAL,CAAkBE,kBAAlB;AACAQ,UAAAA,IAAI,CAACsE,WAAL,CAAiBO,gBAAjB,CAAkCpE,OAAlC,CAA0C,CAACqE,IAAD,EAAOL,KAAP,KAAiB;AACvD,kBAAMC,QAAQ,GAAGL,UAAU,CAACI,KAAD,CAA3B;AACA,kBAAMM,SAAS,GAAG/E,IAAI,CAACsE,WAAL,CAAiBU,iBAAjB,CAAmCP,KAAnC,KAA6C,CAA/D;AACA,iBAAKQ,cAAL,CAAoBP,QAApB,EAA8BK,SAA9B,EAAyCD,IAAzC;AACH,WAJD;AAKH;;AAEOH,QAAAA,MAAM,CAACO,QAAD,EAAmBC,UAAnB,EAAuC;AACjD;AAAA;AAAA,sCAAUC,IAAV,CAAe;AAAA;AAAA,gCAAOlF,IAAP,CAAYmF,UAA3B,EAAuCH,QAAvC,EAAiDlI,WAAjD,EAA8D,CAACsI,GAAD,EAAMC,OAAN,KAA+B;AACzF,gBAAID,GAAJ,EAAS;AACL;AACH;;AACDH,YAAAA,UAAU,CAACK,WAAX,GAAyBD,OAAzB;AACH,WALD;AAMH;;AAEON,QAAAA,cAAc,CAACP,QAAD,EAAmBjB,KAAnB,EAAkCqB,IAAlC,EAAoE;AACtF,gBAAMpF,MAAM,GAAGhD,WAAW,CAAC,KAAK4B,aAAN,CAA1B;AACA,eAAKgB,YAAL,CAAkByB,QAAlB,CAA2BrB,MAA3B;AAEA,gBAAM+F,UAAU,GAAG/F,MAAM,CAACgG,cAAP,CAAsB,aAAtB,EAAqCzE,YAArC,CAAkDlE,MAAlD,CAAnB;AACA,gBAAM4I,WAAW,GAAGjG,MAAM,CAACgG,cAAP,CAAsB,cAAtB,EAAsCzE,YAAtC,CAAmDtE,KAAnD,CAApB;AACA,gBAAMiJ,aAAa,GAAG;AAAA;AAAA,kCAAQhF,cAAR,CAAuB8D,QAAvB,CAAtB;AACA,eAAKC,MAAL,CAAa,eAAciB,aAAa,CAAChB,GAAI,cAA7C,EAA4Da,UAA5D;AACAE,UAAAA,WAAW,CAACpC,MAAZ,GAAsB,GAAEE,KAAM,EAA9B;AAEA,gBAAMoC,QAAQ,GAAGnG,MAAM,CAACgG,cAAP,CAAsB,WAAtB,EAAmCzE,YAAnC,CAAgDlE,MAAhD,CAAjB;AACA,gBAAM+I,SAAS,GAAGpG,MAAM,CAACgG,cAAP,CAAsB,YAAtB,EAAoCzE,YAApC,CAAiDtE,KAAjD,CAAlB;AACA,gBAAMoJ,WAAW,GAAG;AAAA;AAAA,kCAAQnF,cAAR,CAAuBoF,QAAQ,CAAClB,IAAI,CAACmB,MAAN,CAA/B,CAApB;AACA,eAAKtB,MAAL,CAAa,aAAYoB,WAAW,CAACnB,GAAI,cAAzC,EAAwDiB,QAAxD;AACAC,UAAAA,SAAS,CAACvC,MAAV,GAAoB,GAAEuB,IAAI,CAACoB,OAAQ,EAAnC;AACH;;AAEOhH,QAAAA,aAAa,GAAG;AACpB,eAAKkC,YAAL;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWkB,QAAAA,sBAAsB,CAAC6D,QAAgB,GAAG,GAApB,EAAyBC,YAAoB,GAAG,GAAhD,EAAqD;AAC9EhE,UAAAA,OAAO,CAACiE,GAAR,CAAY,iCAAZ,EAD8E,CAG9E;;AACA,gBAAMC,aAAa,GAAG,CAClB;AAAEC,YAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,YAAAA,QAAQ,EAAE;AAAxC,WADkB,EAElB;AAAED,YAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,YAAAA,QAAQ,EAAE;AAAxC,WAFkB,EAGlB;AAAED,YAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,YAAAA,QAAQ,EAAE;AAAxC,WAHkB,EAIlB;AAAED,YAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,YAAAA,QAAQ,EAAE;AAAxC,WAJkB,CAAtB,CAJ8E,CAW9E;;AACAF,UAAAA,aAAa,CAAC7F,OAAd,CAAsB,CAACgG,IAAD,EAAOhC,KAAP,KAAiB;AACnC,kBAAMiC,QAAQ,GAAG,KAAKjF,UAAL,CAAgBiE,cAAhB,CAA+Be,IAAI,CAACF,QAApC,CAAjB;AACA,kBAAMI,QAAQ,GAAG,KAAKC,eAAL,CAAqBlB,cAArB,CAAoCe,IAAI,CAACD,QAAzC,CAAjB;AACA,iBAAKK,cAAL,CAAoBH,QAApB,EAA8BC,QAA9B,EAAwClC,KAAK,GAAG2B,YAAhD,EAA8DD,QAA9D;AACH,WAJD;AAKH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACYU,QAAAA,cAAc,CAACH,QAAD,EAAiBC,QAAjB,EAAiCG,KAAa,GAAG,CAAjD,EAAoDX,QAAgB,GAAG,GAAvE,EAA4E;AAC9F;AACA,gBAAMY,YAAY,GAAGJ,QAAQ,CAAC1F,YAAT,CAAsB/D,WAAtB,EAAoC8J,qBAApC,CAA0D7J,IAAI,CAAC8J,IAA/D,CAArB,CAF8F,CAG9F;;AACA,gBAAMC,cAAc,GAAGR,QAAQ,CAACrH,MAAT,CAAiB4B,YAAjB,CAA8B/D,WAA9B,EAA4CiK,oBAA5C,CAAiEJ,YAAjE,CAAvB;AAEA3E,UAAAA,OAAO,CAACiE,GAAR,CAAa,UAASK,QAAQ,CAACU,IAAK,OAAMV,QAAQ,CAACW,QAAT,CAAkBC,CAAE,KAAIZ,QAAQ,CAACW,QAAT,CAAkBE,CAAE,QAAOL,cAAc,CAACI,CAAE,KAAIJ,cAAc,CAACK,CAAE,GAAnI,EAN8F,CAQ9F;;AACAtK,UAAAA,KAAK,CAACyJ,QAAD,CAAL,CACKI,KADL,CACWA,KADX,EAEKU,EAFL,CAEQrB,QAFR,EAEkB;AAAEkB,YAAAA,QAAQ,EAAEH;AAAZ,WAFlB,EAEgD;AAAEO,YAAAA,MAAM,EAAE;AAAV,WAFhD,EAGKC,IAHL,CAGU,MAAM;AACRtF,YAAAA,OAAO,CAACiE,GAAR,CAAa,MAAKK,QAAQ,CAACU,IAAK,WAAUT,QAAQ,CAACS,IAAK,EAAxD;AACA,iBAAK1I,QAAL;AACA,iBAAKiJ,oBAAL,CAA0B,KAAKjJ,QAA/B;AACH,WAPL,EAQKgD,KARL;AASH;;AAEOiG,QAAAA,oBAAoB,CAAClE,KAAD,EAAgB;AACxC,cAAIA,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAKpC,eAAL,CAAqBC,MAArB,GAA8B,KAA9B;AACA,iBAAKC,cAAL,CAAoBD,MAApB,GAA6B,IAA7B;AACA,iBAAKE,YAAL,CAAkBF,MAAlB,GAA2B,KAA3B;AACA,iBAAKhC,YAAL,CAAkBgC,MAAlB,GAA2B,IAA3B;AACA,iBAAKjD,SAAL,CAAeiD,MAAf,GAAwB,KAAxB;AACH;;AAED,cAAImC,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAK,IAAImE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AACxB,oBAAMC,QAAQ,GAAGnL,WAAW,CAAC,KAAK6B,WAAN,CAA5B;AACA,mBAAKkB,OAAL,CAAasB,QAAb,CAAsB8G,QAAtB;AACA,oBAAMhH,QAAQ,GAAGnE,WAAW,CAAC,KAAK8B,WAAN,CAA5B;AACA,mBAAKmB,OAAL,CAAaoB,QAAb,CAAsBF,QAAtB;AAEA,oBAAMiH,QAAQ,GAAG;AAAA;AAAA,sDAAgBC,OAAhB,CAAwB,GAAxB,EAA6BH,CAA7B,CAAjB;;AACA,mBAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAAQ,CAAC1E,MAA7B,EAAqC4E,CAAC,EAAtC,EAA0C;AACtC,sBAAMC,QAAQ,GAAGvL,WAAW,CAAC,KAAK+B,WAAN,CAA5B;AACAoC,gBAAAA,QAAQ,CAACE,QAAT,CAAkBkH,QAAlB;AACH;AACJ;AACJ;AACJ;;AAlSqC,O;;;;;iBAGb,I;;;;;;;iBAEI,I;;;;;;;iBAEH,I;;;;;;;iBAEC,I;;;;;;;iBAGK,I;;;;;;;iBAED,I;;;;;;;iBAEF,I;;;;;;;iBAEA,I;;;;;;;iBAGF,I;;;;;;;iBAEK,I;;;;;;;iBAER,I;;;;;;;iBAEA,I;;;;;;;iBAGO,I", "sourcesContent": ["import { _decorator, Component, instantiate, Label, Node, Prefab, sp, Sprite, SpriteFrame, tween, UITransform, Vec3, v3 } from 'cc';\r\nimport { lobby, yy } from 'yycore';\r\nimport { oops } from '../../core/Oop';\r\nimport { ELIXIR_MAP_LIST, ITEM } from '../../common/GameUtils';\r\nimport { itemcfg } from '../../game/model/ItemCfg';\r\nimport { ElixirViewItem } from './ElixirViewItem';\r\nimport { DigTreasure } from '../../proto/DigTreasureProtocol_client';\r\nimport { Message } from '../../event/MessageManager';\r\nimport { resLoader } from '../../loader/ResLoader';\r\nimport { config } from '../../common/Config';\r\nimport { ViewUtil } from '../../util/ViewUtil';\r\nimport { elixirUnsealCfg } from '../../game/model/ElixirUnsealCfg';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('ElixirView')\r\nexport class ElixirView extends Component {\r\n\r\n    @property(Prefab)\r\n    private pbItem: Prefab = null;\r\n    @property(Node)\r\n    private ndElixirArea: Node = null;\r\n    @property(Label)\r\n    private labTimer: Label = null;\r\n    @property(Label)\r\n    private labLingqi: Label = null;\r\n\r\n    @property(Node)\r\n    private ndBeforeKaiDing: Node = null;\r\n    @property(Node)\r\n    private ndAfterKaiDing: Node = null;\r\n    @property(Node)\r\n    private ndZiYangArea: Node = null;\r\n    @property(Node)\r\n    private ndResultArea: Node = null;\r\n\r\n    @property(Node)\r\n    private ndOpenArea: Node = null;\r\n    @property(Node)\r\n    private ndOpenBlankArea: Node = null;\r\n    @property(Node)\r\n    private ndLines: Node = null;\r\n    @property(Node)\r\n    private ndItems: Node = null;\r\n\r\n    @property(sp.Skeleton)\r\n    private skeLuzi: sp.Skeleton = null;\r\n\r\n    private _kaidingData: DigTreasure.PtNextKaiDingSubInfo = null;\r\n\r\n    private _luziNoot: Node = null;\r\n\r\n    private _resultPrefab: Node = null;\r\n    private _linePrefab: Node = null;\r\n    private _itemPrefab: Node = null;\r\n    private _giftPrefab: Node = null;\r\n\r\n    private _moveNum: number = 0;\r\n\r\n    onLoad() {\r\n        Message.on(\"on_elixir_auto_kaiding_begin\", this.onKaidingBegin, this);\r\n        Message.on(\"on_elixir_auto_kaiding_finish\", this.onKaidingFinish, this);\r\n        Message.on(\"on_elixir_once_kaiding_begin\", this.onKaidingBegin, this);\r\n        Message.on(\"on_elixir_once_kaiding_finish\", this.onKaidingFinish, this);\r\n\r\n        yy.event.on(this, lobby.events.MyPropsChanged, this.onUpdateProps);\r\n\r\n        this._luziNoot = this.skeLuzi.node.parent;\r\n\r\n        this._resultPrefab = instantiate(this.ndResultArea.children[0]);\r\n        this.ndResultArea.destroyAllChildren();\r\n\r\n        this._linePrefab = instantiate(this.ndLines.children[0]);\r\n        this.ndLines.destroyAllChildren();\r\n        const ndItem = this.ndItems.children[0];\r\n        const ndGift = ndItem.children[0];\r\n        this._giftPrefab = instantiate(ndGift);\r\n        ndItem.destroyAllChildren();\r\n        this._itemPrefab = instantiate(ndItem);\r\n        this.ndItems.destroyAllChildren();\r\n    }\r\n\r\n    onDestroy() {\r\n        Message.off(\"on_elixir_auto_kaiding_begin\", this.onKaidingBegin, this);\r\n        Message.off(\"on_elixir_auto_kaiding_finish\", this.onKaidingFinish, this);\r\n        Message.off(\"on_elixir_once_kaiding_begin\", this.onKaidingBegin, this);\r\n        Message.off(\"on_elixir_once_kaiding_finish\", this.onKaidingFinish, this);\r\n\r\n        yy.event.off(lobby.events.MyPropsChanged, this.onUpdateProps);\r\n    }\r\n\r\n    onAdded(data) {\r\n        const kaidingData = oops.game.getNextAutoKaiDingInfo(oops.game.getEMapType());\r\n        this._kaidingData = kaidingData;\r\n\r\n        this.ndElixirArea.destroyAllChildren();\r\n\r\n        const lMapType = oops.game.getLMapType();\r\n        const itemIdList = ELIXIR_MAP_LIST[lMapType];\r\n        itemIdList.forEach(itemId => {\r\n            const itemDataCfg = itemcfg.getDataByIndex(itemId);\r\n            const itemNode = instantiate(this.pbItem);\r\n            this.ndElixirArea.addChild(itemNode);\r\n\r\n            const itemComp = itemNode.getComponent(ElixirViewItem);\r\n            itemComp.init(itemDataCfg);\r\n        });\r\n\r\n        this.updateTimer();\r\n        this.updateLingqi();\r\n\r\n        this.ndBeforeKaiDing.active = true;\r\n        this.ndAfterKaiDing.active = false;\r\n        this.ndZiYangArea.active = true;\r\n        this.ndResultArea.active = false;\r\n        this.ndOpenArea.active = false;\r\n    }\r\n\r\n    start() {\r\n\r\n    }\r\n\r\n    update(dt: number) {\r\n        this.updateTimer();\r\n    }\r\n\r\n    protected onCloseClick() {\r\n        oops.gui.removeByNode(this.node)\r\n    }\r\n\r\n    protected onBtnManulKaiDing() {\r\n        oops.game.m_gameNet.Send_ReqOnceKaiDing();\r\n    }\r\n\r\n    protected onBtnUseAll() {\r\n        console.debug('onBtnUseAll');\r\n        // 测试移动小球功能\r\n        this.moveElixirBallsToSlots();\r\n    }\r\n\r\n    private updateTimer() {\r\n        if (this._kaidingData) {\r\n            const curTime = Date.now() + oops.game.getServerTimeOffset();\r\n            const timeData = Math.floor((this._kaidingData.nextKaiDingTime.toNumber() * 1000 - curTime) / 1000);\r\n            const minutes: number = Math.floor(timeData / 60);\r\n            const remainingSeconds: number = timeData % 60;\r\n            const formattedMinutes: string = minutes.toString().length < 2 ? `0${minutes.toString()}` : minutes.toString();\r\n            const formattedSeconds: string = remainingSeconds.toString().length < 2 ? `0${remainingSeconds.toString()}` : remainingSeconds.toString();\r\n            this.labTimer.string = \"开鼎时间 \" + (minutes <= 0 && remainingSeconds <= 0 ? \"00:00\" : `${formattedMinutes}:${formattedSeconds}`);\r\n        }\r\n    }\r\n\r\n    private updateLingqi() {\r\n        const eMapType = oops.game.getEMapType();\r\n        let count = 0;\r\n        switch (eMapType) {\r\n            case DigTreasure.eMapType.eMT_Shan2:\r\n                count = lobby.myBag.getPropCountByID(ITEM.LINGQI_1) | 0;\r\n                break;\r\n            case DigTreasure.eMapType.eMT_Dong2:\r\n                count = lobby.myBag.getPropCountByID(ITEM.LINGQI_2) | 0;\r\n                break;\r\n            case DigTreasure.eMapType.eMT_Ling2:\r\n                count = lobby.myBag.getPropCountByID(ITEM.LINGQI_3) | 0;\r\n                break;\r\n        }\r\n        this.labLingqi.string = count.toString();\r\n    }\r\n\r\n    private onKaidingBegin(event, data: { batId: string, reclaimXianDanList: DigTreasure.PtGiftAList, bool?: boolean }) {\r\n        // this.ndBeforeKaiDing.active = false;\r\n        // this.ndAfterKaiDing.active = true;\r\n        // this.ndZiYangArea.active = false;\r\n        // this.ndResultArea.active = true;\r\n        // this.ndResultArea.destroyAllChildren();\r\n\r\n        this.skeLuzi.setAnimation(0, \"kai\", false);\r\n        this.scheduleOnce(() => {\r\n            this.ndOpenArea.active = true;\r\n            this.moveElixirBallsToSlots();\r\n        }, 1.2);\r\n    }\r\n\r\n    private onKaidingFinish(event, data: { kaidingPlan: DigTreasure.PtAutoKaiDingPlan, kaidingMingxi: DigTreasure.PtPlayerKaiDingMingXiA }) {\r\n        const elixirList = ELIXIR_MAP_LIST[oops.game.getLMapType()];\r\n\r\n        data.kaidingPlan.kaiDingResults.forEach((result, index) => {\r\n            const elixirId = elixirList[result];\r\n            const sp = this.ndOpenArea.children[index].getComponent(Sprite);\r\n            const itemDataCfg = itemcfg.getDataByIndex(elixirId);\r\n            this.loadSp(`icon/elixir/${itemDataCfg.res}/spriteFrame`, sp);\r\n        });\r\n\r\n        this.ndResultArea.destroyAllChildren();\r\n        data.kaidingPlan.zhanWeiGiftAList.forEach((gift, index) => {\r\n            const elixirId = elixirList[index];\r\n            const costCount = data.kaidingPlan.xianDanCostCounts[index] || 0;\r\n            this.loadResultItem(elixirId, costCount, gift);\r\n        });\r\n    }\r\n\r\n    private loadSp(iconPath: string, spriteNode: Sprite) {\r\n        resLoader.load(config.game.bundleName, iconPath, SpriteFrame, (err, spFrame: SpriteFrame) => {\r\n            if (err) {\r\n                return\r\n            }\r\n            spriteNode.spriteFrame = spFrame;\r\n        });\r\n    }\r\n\r\n    private loadResultItem(elixirId: number, count: number, gift: DigTreasure.PtZhanWeiGiftA) {\r\n        const ndItem = instantiate(this._resultPrefab);\r\n        this.ndResultArea.addChild(ndItem);\r\n\r\n        const elixirIcon = ndItem.getChildByName(\"elixir_icon\").getComponent(Sprite);\r\n        const elixirCount = ndItem.getChildByName(\"elixir_count\").getComponent(Label);\r\n        const elixirDataCfg = itemcfg.getDataByIndex(elixirId);\r\n        this.loadSp(`icon/elixir/${elixirDataCfg.res}/spriteFrame`, elixirIcon);\r\n        elixirCount.string = `${count}`;\r\n\r\n        const giftIcon = ndItem.getChildByName(\"gift_icon\").getComponent(Sprite);\r\n        const giftCount = ndItem.getChildByName(\"gift_count\").getComponent(Label);\r\n        const giftDataCfg = itemcfg.getDataByIndex(parseInt(gift.giftId));\r\n        this.loadSp(`icon/item/${giftDataCfg.res}/spriteFrame`, giftIcon);\r\n        giftCount.string = `${gift.giftNum}`;\r\n    }\r\n\r\n    private onUpdateProps() {\r\n        this.updateLingqi();\r\n    }\r\n\r\n    /**\r\n     * 移动红框中的4个小球到黄框中对应的坑位\r\n     * 从 OpenNode 下的 icon_elixir_01-04 移动到 OpenBox 下的 icon_elixir_bg_1-4\r\n     * @param duration 动画持续时间，默认0.8秒\r\n     * @param delayBetween 每个小球之间的延迟时间，默认0.1秒\r\n     */\r\n    public moveElixirBallsToSlots(duration: number = 0.8, delayBetween: number = 0.1) {\r\n        console.log(\"找到 OpenNode 和 OpenBox 节点，开始移动小球\");\r\n\r\n        // 定义小球和坑位的对应关系\r\n        const ballSlotPairs = [\r\n            { ballName: \"icon_elixir_01\", slotName: \"icon_elixir_bg_1\" },\r\n            { ballName: \"icon_elixir_02\", slotName: \"icon_elixir_bg_2\" },\r\n            { ballName: \"icon_elixir_03\", slotName: \"icon_elixir_bg_3\" },\r\n            { ballName: \"icon_elixir_04\", slotName: \"icon_elixir_bg_4\" }\r\n        ];\r\n\r\n        // 遍历每个小球和对应的坑位\r\n        ballSlotPairs.forEach((pair, index) => {\r\n            const ballNode = this.ndOpenArea.getChildByName(pair.ballName);\r\n            const slotNode = this.ndOpenBlankArea.getChildByName(pair.slotName);\r\n            this.moveBallToSlot(ballNode, slotNode, index * delayBetween, duration);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 移动单个小球到指定坑位\r\n     * @param ballNode 小球节点\r\n     * @param slotNode 坑位节点\r\n     * @param delay 延迟时间\r\n     * @param duration 动画持续时间\r\n     */\r\n    private moveBallToSlot(ballNode: Node, slotNode: Node, delay: number = 0, duration: number = 0.8) {\r\n        // 获取坑位在世界坐标系中的位置\r\n        const slotWorldPos = slotNode.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO);\r\n        // 将坑位的世界坐标转换为小球父节点的本地坐标\r\n        const targetLocalPos = ballNode.parent!.getComponent(UITransform)!.convertToNodeSpaceAR(slotWorldPos);\r\n\r\n        console.log(`准备移动小球 ${ballNode.name} 从 (${ballNode.position.x}, ${ballNode.position.y}) 到 (${targetLocalPos.x}, ${targetLocalPos.y})`);\r\n\r\n        // 使用 tween 动画移动小球\r\n        tween(ballNode)\r\n            .delay(delay)\r\n            .to(duration, { position: targetLocalPos }, { easing: 'cubicOut' })\r\n            .call(() => {\r\n                console.log(`小球 ${ballNode.name} 已移动到坑位 ${slotNode.name}`);\r\n                this._moveNum++;\r\n                this.switchToAfterKaiDing(this._moveNum);\r\n            })\r\n            .start();\r\n    }\r\n\r\n    private switchToAfterKaiDing(count: number) {\r\n        if (count == 1) {\r\n            this.ndBeforeKaiDing.active = false;\r\n            this.ndAfterKaiDing.active = true;\r\n            this.ndZiYangArea.active = false;\r\n            this.ndResultArea.active = true;\r\n            this._luziNoot.active = false;\r\n        }\r\n\r\n        if (count == 4) {\r\n            for (let i = 0; i < 4; i++) {\r\n                const lineNode = instantiate(this._linePrefab);\r\n                this.ndLines.addChild(lineNode);\r\n                const itemNode = instantiate(this._itemPrefab);\r\n                this.ndItems.addChild(itemNode);\r\n\r\n                const dataList = elixirUnsealCfg.getData(100, i);\r\n                for (let j = 0; j < dataList.length; j++) {\r\n                    const giftNode = instantiate(this._giftPrefab);\r\n                    itemNode.addChild(giftNode);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n\r\n"]}