System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, JsonUtil, ElixirUnsealCfg, _crd, elixirUnsealCfg;

  function _reportPossibleCrUseOfJsonUtil(extras) {
    _reporterNs.report("JsonUtil", "../../util/JsonUtil", _context.meta, extras);
  }

  _export("ElixirUnsealCfg", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      JsonUtil = _unresolved_2.JsonUtil;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d2205pNmehBl7HaCNLgQWIl", "ElixirUnsealCfg", undefined);

      /** 二次解析封装（策划Excel导出的Json静态数据） */
      _export("ElixirUnsealCfg", ElixirUnsealCfg = class ElixirUnsealCfg {
        constructor() {
          /** 静态表中一条数据 */
          this.data = void 0;
        }

        init() {
          if (!!this.data) return this.data;
          this.data = new Map();
          var table = (_crd && JsonUtil === void 0 ? (_reportPossibleCrUseOfJsonUtil({
            error: Error()
          }), JsonUtil) : JsonUtil).get(ElixirUnsealCfg.TableName);
          let map1 = new Map();
          let map2 = new Map();
          let map3 = new Map();

          for (let index = 1; index <= Object.keys(table).length; index++) {
            const ele = table[index];

            if (ele.map == 100) {
              const drawList = map1.get(ele.draw) || [];
              drawList.push(ele);
              map1.set(ele.draw, drawList);
            } else if (ele.map == 1000) {
              const drawList = map2.get(ele.draw) || [];
              drawList.push(ele);
              map2.set(ele.draw, drawList);
            } else if (ele.map == 10000) {
              const drawList = map3.get(ele.draw) || [];
              drawList.push(ele);
              map3.set(ele.draw, drawList);
            }
          }

          this.data.set(map1[0].map, map1);
          this.data.set(map2[0].map, map2);
          this.data.set(map3[0].map, map3);
        }

        getData(map, draw) {
          return this.data.get(map).get(draw);
        }

        getDataByMap(map) {
          return this.data.get(map);
        }

      });

      ElixirUnsealCfg.TableName = "elixir_unseal";

      _export("elixirUnsealCfg", elixirUnsealCfg = new ElixirUnsealCfg());

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0f948b5318cfd26c03b0171d952c7bcab14d174e.js.map