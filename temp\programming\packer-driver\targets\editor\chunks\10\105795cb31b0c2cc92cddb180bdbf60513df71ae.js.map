{"version": 3, "sources": ["file:///F:/SVN/plinko/BasePro/DemoMoveV3/assets/scripts/game/model/ElixirUnsealCfg.ts"], "names": ["ElixirUnsealCfg", "<PERSON><PERSON><PERSON><PERSON>", "data", "init", "Map", "table", "get", "TableName", "map1", "map2", "map3", "index", "Object", "keys", "length", "ele", "map", "drawList", "draw", "push", "set", "getData", "getDataByMap", "elixirUnsealCfg"], "mappings": ";;;wCAYaA,e;;;;;;;;;;;;;;AAZJC,MAAAA,Q,iBAAAA,Q;;;;;;;AAWT;iCACaD,e,GAAN,MAAMA,eAAN,CAAsB;AAAA;AAGzB;AAHyB,eAIjBE,IAJiB;AAAA;;AAMzBC,QAAAA,IAAI,GAAG;AACH,cAAI,CAAC,CAAC,KAAKD,IAAX,EAAiB,OAAO,KAAKA,IAAZ;AACjB,eAAKA,IAAL,GAAY,IAAIE,GAAJ,EAAZ;AACA,cAAIC,KAAK,GAAG;AAAA;AAAA,oCAASC,GAAT,CAAaN,eAAe,CAACO,SAA7B,CAAZ;AACA,cAAIC,IAAI,GAAG,IAAIJ,GAAJ,EAAX;AACA,cAAIK,IAAI,GAAG,IAAIL,GAAJ,EAAX;AACA,cAAIM,IAAI,GAAG,IAAIN,GAAJ,EAAX;;AACA,eAAK,IAAIO,KAAK,GAAG,CAAjB,EAAoBA,KAAK,IAAIC,MAAM,CAACC,IAAP,CAAYR,KAAZ,EAAmBS,MAAhD,EAAwDH,KAAK,EAA7D,EAAiE;AAC7D,kBAAMI,GAAG,GAAGV,KAAK,CAACM,KAAD,CAAjB;;AACA,gBAAII,GAAG,CAACC,GAAJ,IAAW,GAAf,EAAoB;AAChB,oBAAMC,QAAQ,GAAGT,IAAI,CAACF,GAAL,CAASS,GAAG,CAACG,IAAb,KAAsB,EAAvC;AACAH,cAAAA,GAAG,CAACC,GAAJ,GAAU,CAAV;AACAC,cAAAA,QAAQ,CAACE,IAAT,CAAcJ,GAAd;AACAP,cAAAA,IAAI,CAACY,GAAL,CAASL,GAAG,CAACG,IAAb,EAAmBD,QAAnB;AACH,aALD,MAKO,IAAIF,GAAG,CAACC,GAAJ,IAAW,IAAf,EAAqB;AACxB,oBAAMC,QAAQ,GAAGR,IAAI,CAACH,GAAL,CAASS,GAAG,CAACG,IAAb,KAAsB,EAAvC;AACAH,cAAAA,GAAG,CAACC,GAAJ,GAAU,CAAV;AACAC,cAAAA,QAAQ,CAACE,IAAT,CAAcJ,GAAd;AACAN,cAAAA,IAAI,CAACW,GAAL,CAASL,GAAG,CAACG,IAAb,EAAmBD,QAAnB;AACH,aALM,MAKA,IAAIF,GAAG,CAACC,GAAJ,IAAW,KAAf,EAAsB;AACzB,oBAAMC,QAAQ,GAAGP,IAAI,CAACJ,GAAL,CAASS,GAAG,CAACG,IAAb,KAAsB,EAAvC;AACAH,cAAAA,GAAG,CAACC,GAAJ,GAAU,CAAV;AACAC,cAAAA,QAAQ,CAACE,IAAT,CAAcJ,GAAd;AACAL,cAAAA,IAAI,CAACU,GAAL,CAASL,GAAG,CAACG,IAAb,EAAmBD,QAAnB;AACH;AACJ;;AACD,eAAKf,IAAL,CAAUkB,GAAV,CAAc,CAAd,EAAiBZ,IAAjB;AACA,eAAKN,IAAL,CAAUkB,GAAV,CAAc,CAAd,EAAiBX,IAAjB;AACA,eAAKP,IAAL,CAAUkB,GAAV,CAAc,CAAd,EAAiBV,IAAjB;AACH;;AAEDW,QAAAA,OAAO,CAACL,GAAD,EAAcE,IAAd,EAAgD;AACnD,iBAAO,KAAKhB,IAAL,CAAUI,GAAV,CAAcU,GAAd,EAAmBV,GAAnB,CAAuBY,IAAvB,CAAP;AACH;;AAEDI,QAAAA,YAAY,CAACN,GAAD,EAA+C;AACvD,iBAAO,KAAKd,IAAL,CAAUI,GAAV,CAAcU,GAAd,CAAP;AACH;;AA3CwB,O;;AAAhBhB,MAAAA,e,CACFO,S,GAAoB,e;;iCA8ClBgB,e,GAAkB,IAAIvB,eAAJ,E", "sourcesContent": ["import { JsonUtil } from \"../../util/JsonUtil\";\r\n\r\nexport interface ElixirUnsealType {\r\n    map: number,\r\n    draw: number,\r\n    gift: number,\r\n    name: string,\r\n    value: number,\r\n    count: number,\r\n}\r\n\r\n/** 二次解析封装（策划Excel导出的Json静态数据） */\r\nexport class ElixirUnsealCfg {\r\n    static TableName: string = \"elixir_unseal\";\r\n\r\n    /** 静态表中一条数据 */\r\n    private data: Map<number, Map<number, ElixirUnsealType[]>>;\r\n\r\n    init() {\r\n        if (!!this.data) return this.data;\r\n        this.data = new Map();\r\n        var table = JsonUtil.get(ElixirUnsealCfg.TableName);\r\n        let map1 = new Map<number, ElixirUnsealType[]>();\r\n        let map2 = new Map<number, ElixirUnsealType[]>();\r\n        let map3 = new Map<number, ElixirUnsealType[]>();\r\n        for (let index = 1; index <= Object.keys(table).length; index++) {\r\n            const ele = table[index];\r\n            if (ele.map == 100) {\r\n                const drawList = map1.get(ele.draw) || [];\r\n                ele.map = 4;\r\n                drawList.push(ele);\r\n                map1.set(ele.draw, drawList);\r\n            } else if (ele.map == 1000) {\r\n                const drawList = map2.get(ele.draw) || [];\r\n                ele.map = 5;\r\n                drawList.push(ele);\r\n                map2.set(ele.draw, drawList);\r\n            } else if (ele.map == 10000) {\r\n                const drawList = map3.get(ele.draw) || [];\r\n                ele.map = 6;\r\n                drawList.push(ele);\r\n                map3.set(ele.draw, drawList);\r\n            }\r\n        }\r\n        this.data.set(4, map1);\r\n        this.data.set(5, map2);\r\n        this.data.set(6, map3);\r\n    }\r\n\r\n    getData(map: number, draw: number): ElixirUnsealType[] {\r\n        return this.data.get(map).get(draw);\r\n    }\r\n\r\n    getDataByMap(map: number): Map<number, ElixirUnsealType[]> {\r\n        return this.data.get(map);\r\n    }\r\n\r\n}\r\n\r\nexport const elixirUnsealCfg = new ElixirUnsealCfg();\r\n"]}