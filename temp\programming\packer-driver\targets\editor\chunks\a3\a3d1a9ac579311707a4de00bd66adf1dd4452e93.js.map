{"version": 3, "sources": ["file:///F:/SVN/plinko/BasePro/DemoMoveV3/assets/scripts/ui/hud/ElixirNode.ts"], "names": ["_decorator", "Component", "Label", "oops", "UIID", "Message", "tips", "elixirUnsealCfg", "ccclass", "property", "ElixirNode", "onceBeginData", "autoBeginData", "onLoad", "init", "on", "onKaidingBegin", "onKaidingFinish", "onKaidingAbort", "onKaidingMingXiResult", "update", "dt", "updateLabel", "kaidingData", "game", "getNextAutoKaiDingInfo", "getEMapType", "countDownCheck", "curTime", "Date", "now", "getServerTimeOffset", "timeData", "nextKaiDingTime", "toNumber", "onBtnManulElixir", "console", "debug", "gui", "has", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "open", "batId", "nextBatId", "labOrder", "string", "getLastDashSurroundingNumbers", "Math", "floor", "minutes", "remainingSeconds", "formattedMinutes", "toString", "length", "formattedSeconds", "labTimer", "event", "data", "bool", "m_gameNet", "Send_ReqSelfKaiDingMingXi", "batID", "CoutDownNode", "remove", "batIdStr", "content", "showTipsPop", "eMapType", "mingxiListData", "getKaiDingMingXiListData", "mxData", "opMingXi", "JSON", "parse", "weiChuCounts", "i", "jushu", "labNearlyList"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AACvBC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,O,iBAAAA,O;;AAEAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,e,iBAAAA,e;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;4BAGjBU,U,WADZF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAAC,CAACP,KAAD,CAAD,C,2BATb,MACaQ,UADb,SACgCT,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAW9BU,aAX8B,GAWd,IAXc;AAAA,eAY9BC,aAZ8B,GAYd,IAZc;AAAA;;AActCC,QAAAA,MAAM,GAAG;AACL;AAAA;AAAA,kDAAgBC,IAAhB;AAEA;AAAA;AAAA,kCAAQC,EAAR,CAAW,8BAAX,EAA2C,KAAKC,cAAhD,EAAgE,IAAhE;AACA;AAAA;AAAA,kCAAQD,EAAR,CAAW,+BAAX,EAA4C,KAAKE,eAAjD,EAAkE,IAAlE;AACA;AAAA;AAAA,kCAAQF,EAAR,CAAW,8BAAX,EAA2C,KAAKG,cAAhD,EAAgE,IAAhE;AACA;AAAA;AAAA,kCAAQH,EAAR,CAAW,8BAAX,EAA2C,KAAKI,qBAAhD,EAAuE,IAAvE;AACH;;AAEDC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,eAAKC,WAAL;AAEA,gBAAMC,WAAW,GAAG;AAAA;AAAA,4BAAKC,IAAL,CAAUC,sBAAV,CAAiC;AAAA;AAAA,4BAAKD,IAAL,CAAUE,WAAV,EAAjC,CAApB;;AACA,cAAIH,WAAW,IAAI,CAAC;AAAA;AAAA,4BAAKC,IAAL,CAAUG,cAA9B,EAA8C;AAC1C,kBAAMC,OAAO,GAAGC,IAAI,CAACC,GAAL,KAAa;AAAA;AAAA,8BAAKN,IAAL,CAAUO,mBAAV,EAA7B;AACA,kBAAMC,QAAQ,GAAG,CAACT,WAAW,CAACU,eAAZ,CAA4BC,QAA5B,KAAyC,IAAzC,GAAgDN,OAAjD,IAA4D,IAA7E;;AACA,gBAAII,QAAQ,GAAG,CAAf,EAAkB,CACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AACJ;AACJ;;AAESG,QAAAA,gBAAgB,GAAG;AACzBC,UAAAA,OAAO,CAACC,KAAR,CAAc,kBAAd;;AACA,cAAI,CAAC;AAAA;AAAA,4BAAKC,GAAL,CAASC,GAAT,CAAa;AAAA;AAAA,4BAAKC,UAAlB,CAAL,EAAoC;AAChC;AAAA;AAAA,8BAAKF,GAAL,CAASG,IAAT,CAAc;AAAA;AAAA,8BAAKD,UAAnB;AACH;AACJ;;AAEOlB,QAAAA,WAAW,GAAG;AAClB,cAAIC,WAAW,GAAG;AAAA;AAAA,4BAAKC,IAAL,CAAUC,sBAAV,CAAiC;AAAA;AAAA,4BAAKD,IAAL,CAAUE,WAAV,EAAjC,CAAlB;;AACA,cAAIH,WAAJ,EAAiB;AACb,kBAAMmB,KAAK,GAAGnB,WAAW,CAACoB,SAA1B;AACA,iBAAKC,QAAL,CAAcC,MAAd,GAAuB;AAAA;AAAA,8BAAKrB,IAAL,CAAUsB,6BAAV,CAAwCJ,KAAxC,CAAvB;AAEA,kBAAMd,OAAO,GAAGC,IAAI,CAACC,GAAL,KAAa;AAAA;AAAA,8BAAKN,IAAL,CAAUO,mBAAV,EAA7B;AACA,kBAAMC,QAAQ,GAAGe,IAAI,CAACC,KAAL,CAAW,CAACzB,WAAW,CAACU,eAAZ,CAA4BC,QAA5B,KAAyC,IAAzC,GAAgDN,OAAjD,IAA4D,IAAvE,CAAjB;AACA,kBAAMqB,OAAe,GAAGF,IAAI,CAACC,KAAL,CAAWhB,QAAQ,GAAG,EAAtB,CAAxB;AACA,kBAAMkB,gBAAwB,GAAGlB,QAAQ,GAAG,EAA5C;AACA,kBAAMmB,gBAAwB,GAAGF,OAAO,CAACG,QAAR,GAAmBC,MAAnB,GAA4B,CAA5B,GAAiC,IAAGJ,OAAO,CAACG,QAAR,EAAmB,EAAvD,GAA2DH,OAAO,CAACG,QAAR,EAA5F;AACA,kBAAME,gBAAwB,GAAGJ,gBAAgB,CAACE,QAAjB,GAA4BC,MAA5B,GAAqC,CAArC,GAA0C,IAAGH,gBAAgB,CAACE,QAAjB,EAA4B,EAAzE,GAA6EF,gBAAgB,CAACE,QAAjB,EAA9G;AACA,iBAAKG,QAAL,CAAcV,MAAd,GAAuBI,OAAO,IAAI,CAAX,IAAgBC,gBAAgB,IAAI,CAApC,GAAwC,OAAxC,GAAmD,GAAEC,gBAAiB,IAAGG,gBAAiB,EAAjH;AACH;AACJ;;AAEOtC,QAAAA,cAAc,CAACwC,KAAD,EAAQC,IAAR,EAA8F;AAChHA,UAAAA,IAAI,CAACC,IAAL,GAAY,IAAZ;AACA,eAAK9C,aAAL,GAAqB6C,IAArB;AACH;;AAEOxC,QAAAA,eAAe,GAAG;AACtB;AAAA;AAAA,4BAAKO,IAAL,CAAUmC,SAAV,CAAoBC,yBAApB,CAA8C;AAAA;AAAA,4BAAKpC,IAAL,CAAUE,WAAV,EAA9C;AACH;;AAEOR,QAAAA,cAAc,CAACsC,KAAD,EAAQK,KAAR,EAAuB;AACzC,cAAI;AAAA;AAAA,4BAAKvB,GAAL,CAASC,GAAT,CAAa;AAAA;AAAA,4BAAKuB,YAAlB,CAAJ,EAAqC;AACjC;AAAA;AAAA,8BAAKxB,GAAL,CAASyB,MAAT,CAAgB;AAAA;AAAA,8BAAKD,YAArB;AACH;;AACD,cAAIE,QAAQ,GAAG;AAAA;AAAA,4BAAKxC,IAAL,CAAUsB,6BAAV,CAAwCe,KAAxC,CAAf;AACA,cAAII,OAAO,GAAI,IAAGD,QAAS,mBAA3B,CALyC,CAMzC;;AACA;AAAA;AAAA,4BAAKE,WAAL,CAAiBD,OAAjB,EAA0B,IAA1B,EAAgC,IAAhC,EAAsC,KAAtC,EAA6C,IAA7C;AACA,eAAK3C,WAAL;AACH;;AAEOH,QAAAA,qBAAqB,GAAG;AAC5B,gBAAMgD,QAAQ,GAAG;AAAA;AAAA,4BAAK3C,IAAL,CAAUE,WAAV,EAAjB;AACA,gBAAM0C,cAAc,GAAG;AAAA;AAAA,4BAAK5C,IAAL,CAAU6C,wBAAV,CAAmCF,QAAnC,CAAvB;;AACA,cAAIC,cAAJ,EAAoB;AAChB,kBAAME,MAAM,GAAGF,cAAc,CAACA,cAAc,CAACf,MAAf,GAAwB,CAAzB,CAA7B;;AACA,gBAAIiB,MAAM,IAAIA,MAAM,CAACC,QAArB,EAA+B;AAC3B,oBAAMA,QAAQ,GAAGC,IAAI,CAACC,KAAL,CAAWH,MAAM,CAACC,QAAlB,CAAjB;;AACA,kBAAIA,QAAQ,CAACG,YAAT,CAAsBrB,MAA1B,EAAkC;AAC9B,qBAAK,IAAIsB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,QAAQ,CAACG,YAAT,CAAsBrB,MAA1C,EAAkDsB,CAAC,EAAnD,EAAuD;AACnD,sBAAIC,KAAK,GAAGL,QAAQ,CAACG,YAAT,CAAsBC,CAAtB,CAAZ;AACA,uBAAKE,aAAL,CAAmBF,CAAnB,EAAsB9B,MAAtB,GAA+B+B,KAAK,IAAI,CAAT,GAAc,GAAEA,KAAM,KAAtB,GAA6B,MAA5D;AACH;AACJ;AACJ;AACJ;AACJ;;AArGqC,O;;;;;iBAGZ,I;;;;;;;iBAGA,I;;;;;;;iBAGO,E", "sourcesContent": ["import { _decorator, Component, Label, Node } from 'cc';\r\nimport { oops } from '../../core/Oop';\r\nimport { UIID } from '../../common/GameUIConfig';\r\nimport { Message } from '../../event/MessageManager';\r\nimport { DigTreasure } from '../../proto/DigTreasureProtocol_client';\r\nimport { tips } from '../../manager/TipsManager';\r\nimport { elixirUnsealCfg } from '../../game/model/ElixirUnsealCfg';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('ElixirNode')\r\nexport class ElixirNode extends Component {\r\n\r\n    @property(Label)\r\n    private labOrder: Label = null;\r\n\r\n    @property(Label)\r\n    private labTimer: Label = null;\r\n\r\n    @property([Label])\r\n    private labNearlyList: Label[] = [];\r\n\r\n    private onceBeginData = null;\r\n    private autoBeginData = null;\r\n\r\n    onLoad() {\r\n        elixirUnsealCfg.init();\r\n\r\n        Message.on(\"on_elixir_auto_kaiding_begin\", this.onKaidingBegin, this);\r\n        Message.on(\"on_elixir_auto_kaiding_finish\", this.onKaidingFinish, this);\r\n        Message.on(\"on_elixir_auto_kaiding_abort\", this.onKaidingAbort, this);\r\n        Message.on(\"elixir_kaiding_mingxi_result\", this.onKaidingMingXiResult, this);\r\n    }\r\n\r\n    update(dt: number) {\r\n        this.updateLabel();\r\n\r\n        const kaidingData = oops.game.getNextAutoKaiDingInfo(oops.game.getEMapType());\r\n        if (kaidingData && !oops.game.countDownCheck) {\r\n            const curTime = Date.now() + oops.game.getServerTimeOffset();\r\n            const timeData = (kaidingData.nextKaiDingTime.toNumber() * 1000 - curTime) / 1000;\r\n            if (timeData < 6) {\r\n                // if (oops.gui.has(UIID.TakeBackView)) {\r\n                //     const nodeTake = oops.gui.getNode(UIID.TakeBackView);\r\n                //     const takeBackView = nodeTake.getComponent(TakeBackView) as TakeBackView;\r\n                //     if (takeBackView && !takeBackView.getRecaliamState()) {\r\n                //         return\r\n                //     }\r\n                // }\r\n                // this.showCountDownNode(reclaimData.nextBaoWuAutoReclaimTime.toNumber());\r\n            }\r\n        }\r\n    }\r\n\r\n    protected onBtnManulElixir() {\r\n        console.debug('onBtnManulElixir');\r\n        if (!oops.gui.has(UIID.ElixirView)) {\r\n            oops.gui.open(UIID.ElixirView);\r\n        }\r\n    }\r\n\r\n    private updateLabel() {\r\n        let kaidingData = oops.game.getNextAutoKaiDingInfo(oops.game.getEMapType());\r\n        if (kaidingData) {\r\n            const batId = kaidingData.nextBatId;\r\n            this.labOrder.string = oops.game.getLastDashSurroundingNumbers(batId);\r\n\r\n            const curTime = Date.now() + oops.game.getServerTimeOffset();\r\n            const timeData = Math.floor((kaidingData.nextKaiDingTime.toNumber() * 1000 - curTime) / 1000);\r\n            const minutes: number = Math.floor(timeData / 60);\r\n            const remainingSeconds: number = timeData % 60;\r\n            const formattedMinutes: string = minutes.toString().length < 2 ? `0${minutes.toString()}` : minutes.toString();\r\n            const formattedSeconds: string = remainingSeconds.toString().length < 2 ? `0${remainingSeconds.toString()}` : remainingSeconds.toString();\r\n            this.labTimer.string = minutes <= 0 && remainingSeconds <= 0 ? \"00:00\" : `${formattedMinutes}:${formattedSeconds}`;\r\n        }\r\n    }\r\n\r\n    private onKaidingBegin(event, data: { batId: string, reclaimXianDanList: DigTreasure.PtGiftAList, bool?: boolean }) {\r\n        data.bool = true;\r\n        this.autoBeginData = data;\r\n    }\r\n\r\n    private onKaidingFinish() {\r\n        oops.game.m_gameNet.Send_ReqSelfKaiDingMingXi(oops.game.getEMapType());\r\n    }\r\n\r\n    private onKaidingAbort(event, batID: string) {\r\n        if (oops.gui.has(UIID.CoutDownNode)) {\r\n            oops.gui.remove(UIID.CoutDownNode);\r\n        }\r\n        let batIdStr = oops.game.getLastDashSurroundingNumbers(batID);\r\n        let content = `【${batIdStr}】本次仙丹开鼎异常，请等待下次开鼎`;\r\n        //弹窗提示\r\n        tips.showTipsPop(content, null, null, false, \"提示\")\r\n        this.updateLabel();\r\n    }\r\n\r\n    private onKaidingMingXiResult() {\r\n        const eMapType = oops.game.getEMapType();\r\n        const mingxiListData = oops.game.getKaiDingMingXiListData(eMapType);\r\n        if (mingxiListData) {\r\n            const mxData = mingxiListData[mingxiListData.length - 1];\r\n            if (mxData && mxData.opMingXi) {\r\n                const opMingXi = JSON.parse(mxData.opMingXi);\r\n                if (opMingXi.weiChuCounts.length) {\r\n                    for (let i = 0; i < opMingXi.weiChuCounts.length; i++) {\r\n                        let jushu = opMingXi.weiChuCounts[i];\r\n                        this.labNearlyList[i].string = jushu >= 2 ? `${jushu}局未出` : \"最近出现\";\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n\r\n"]}